{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"containerAppName": {"type": "string", "defaultValue": "sap-playwright-enterprise-10001", "metadata": {"description": "Name of the container app for SAP Playwright monitoring."}}, "location": {"type": "string", "defaultValue": "germanywestcentral", "metadata": {"description": "Location for all resources."}}, "keyVaultName": {"type": "string", "metadata": {"description": "Name of the existing Key Vault containing SAP credentials."}}, "storageAccountName": {"type": "string", "metadata": {"description": "Name of the existing storage account for monitoring results and screenshots."}}, "containerRegistryName": {"type": "string", "metadata": {"description": "Name of the existing container registry."}}, "containerImageName": {"type": "string", "defaultValue": "sap-playwright-enterprise:latest", "metadata": {"description": "Container image name and tag."}}, "scheduleCron": {"type": "string", "defaultValue": "*/5 * * * *", "metadata": {"description": "Cron expression for scheduling (default: every 5 minutes)."}}, "sapTargetUrl": {"type": "string", "metadata": {"description": "SAP Launchpad URL to monitor."}}, "splunkUrl": {"type": "string", "defaultValue": "", "metadata": {"description": "Splunk HTTP Event Collector URL (optional)."}}, "splunkToken": {"type": "securestring", "defaultValue": "", "metadata": {"description": "Splunk HTTP Event Collector token (optional)."}}}, "variables": {"containerAppEnvironmentName": "[concat(parameters('containerAppName'), '-env')]", "logAnalyticsWorkspaceName": "[concat(parameters('containerAppName'), '-logs')]", "containerRegistryLoginServer": "[concat(parameters('containerRegistryName'), '.azurecr.io')]"}, "resources": [{"type": "Microsoft.OperationalInsights/workspaces", "apiVersion": "2021-06-01", "name": "[variables('logAnalyticsWorkspaceName')]", "location": "[parameters('location')]", "properties": {"sku": {"name": "PerGB2018"}, "retentionInDays": 30}}, {"type": "Microsoft.App/managedEnvironments", "apiVersion": "2022-10-01", "name": "[variables('containerAppEnvironmentName')]", "location": "[parameters('location')]", "dependsOn": ["[resourceId('Microsoft.OperationalInsights/workspaces', variables('logAnalyticsWorkspaceName'))]"], "properties": {"appLogsConfiguration": {"destination": "log-analytics", "logAnalyticsConfiguration": {"customerId": "[reference(resourceId('Microsoft.OperationalInsights/workspaces', variables('logAnalyticsWorkspaceName'))).customerId]", "sharedKey": "[listKeys(resourceId('Microsoft.OperationalInsights/workspaces', variables('logAnalyticsWorkspaceName')), '2021-06-01').primarySharedKey]"}}}}, {"type": "Microsoft.App/containerApps", "apiVersion": "2022-10-01", "name": "[parameters('containerAppName')]", "location": "[parameters('location')]", "dependsOn": ["[resourceId('Microsoft.App/managedEnvironments', variables('containerAppEnvironmentName'))]"], "identity": {"type": "SystemAssigned"}, "properties": {"managedEnvironmentId": "[resourceId('Microsoft.App/managedEnvironments', variables('containerAppEnvironmentName'))]", "workloadProfileName": "Consumption", "configuration": {"secrets": [{"name": "acr-password", "value": "[listCredentials(resourceId('Microsoft.ContainerRegistry/registries', parameters('containerRegistryName')), '2019-05-01').passwords[0].value]"}, {"name": "splunk-token", "value": "[parameters('splunkToken')]"}], "registries": [{"server": "[variables('containerRegistryLoginServer')]", "username": "[parameters('containerRegistryName')]", "passwordSecretRef": "acr-password"}], "activeRevisionsMode": "Single"}, "template": {"containers": [{"name": "sap-playwright-monitor", "image": "[concat(variables('containerRegistryLoginServer'), '/', parameters('containerImageName'))]", "env": [{"name": "KEY_VAULT_NAME", "value": "[parameters('keyVaultName')]"}, {"name": "STORAGE_ACCOUNT_NAME", "value": "[parameters('storageAccountName')]"}, {"name": "STORAGE_CONTAINER_NAME", "value": "sap-monitoring-results"}, {"name": "TARGET_URL", "value": "[parameters('sapTargetUrl')]"}, {"name": "SAP_KEYVAULT_SECRET", "value": "SAP-LAUNCHPAD-CONFIG"}, {"name": "BLOB_PREFIX", "value": "10001_SAP_PLAYWRIGHT"}, {"name": "BROWSER_TYPE", "value": "chromium"}, {"name": "HEADLESS", "value": "true"}, {"name": "TAKE_SCREENSHOT", "value": "true"}, {"name": "PAGE_TIMEOUT", "value": "60000"}, {"name": "NAVIGATION_TIMEOUT", "value": "30000"}, {"name": "SPLUNK_ENABLED", "value": "[if(empty(parameters('splunkUrl')), 'false', 'true')]"}, {"name": "SPLUNK_URL", "value": "[parameters('splunkUrl')]"}, {"name": "SPLUNK_TOKEN", "secretRef": "splunk-token"}], "resources": {"cpu": 2.0, "memory": "4Gi"}}], "scale": {"minReplicas": 0, "maxReplicas": 1, "rules": [{"name": "cron-scale-rule", "custom": {"type": "cron", "metadata": {"timezone": "Central European Standard Time", "start": "0 0 * * *", "end": "59 23 * * *", "desiredReplicas": "1"}}}]}}}}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[guid(parameters('containerAppName'), 'KeyVaultSecretUser')]", "scope": "[concat('Microsoft.KeyVault/vaults/', parameters('keyVaultName'))]", "dependsOn": ["[resourceId('Microsoft.App/containerApps', parameters('containerAppName'))]"], "properties": {"roleDefinitionId": "[concat('/subscriptions/', subscription().subscriptionId, '/providers/Microsoft.Authorization/roleDefinitions/4633458b-17de-408a-b874-0445c86b69e6')]", "principalId": "[reference(resourceId('Microsoft.App/containerApps', parameters('containerAppName')), '2022-10-01', 'Full').identity.principalId]", "principalType": "ServicePrincipal"}}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[guid(parameters('containerAppName'), 'StorageBlobDataContributor')]", "scope": "[concat('Microsoft.Storage/storageAccounts/', parameters('storageAccountName'))]", "dependsOn": ["[resourceId('Microsoft.App/containerApps', parameters('containerAppName'))]"], "properties": {"roleDefinitionId": "[concat('/subscriptions/', subscription().subscriptionId, '/providers/Microsoft.Authorization/roleDefinitions/ba92f5b4-2d11-453d-a403-e96b0029c9fe')]", "principalId": "[reference(resourceId('Microsoft.App/containerApps', parameters('containerAppName')), '2022-10-01', 'Full').identity.principalId]", "principalType": "ServicePrincipal"}}], "outputs": {"containerAppName": {"type": "string", "value": "[parameters('containerAppName')]"}, "containerAppResourceId": {"type": "string", "value": "[resourceId('Microsoft.App/containerApps', parameters('containerAppName'))]"}, "logAnalyticsWorkspaceName": {"type": "string", "value": "[variables('logAnalyticsWorkspaceName')]"}, "managedEnvironmentName": {"type": "string", "value": "[variables('containerAppEnvironmentName')]"}}}