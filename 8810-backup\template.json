{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"containerAppName": {"type": "string", "defaultValue": "monscript8810", "metadata": {"description": "Name of the container app."}}, "location": {"type": "string", "defaultValue": "germanywestcentral", "metadata": {"description": "Location of the resources."}}, "keyVaultName": {"type": "string", "metadata": {"description": "Name of the existing Key Vault."}}, "storageAccountName": {"type": "string", "metadata": {"description": "Name of the existing storage account for monitoring results."}}, "containerRegistryName": {"type": "string", "metadata": {"description": "Name of the existing container registry (in lowercase)."}}, "containerImageName": {"type": "string", "defaultValue": "puw-monitoring:latest", "metadata": {"description": "Container image name and tag."}}, "scheduleCron": {"type": "string", "defaultValue": "0 */2 * * *", "metadata": {"description": "Cron expression for scheduling (default: every 2 hours)."}}}, "variables": {"logAnalyticsWorkspaceName": "[concat(parameters('containerAppName'), '-logs')]", "containerAppEnvironmentName": "[concat(parameters('containerAppName'), '-env')]", "managedIdentityName": "[concat(parameters('containerAppName'), '-identity')]", "containerName": "[replace(parameters('containerAppName'), '-', '_')]", "lowerCaseRegistryName": "[toLower(parameters('containerRegistryName'))]"}, "resources": [{"type": "Microsoft.ManagedIdentity/userAssignedIdentities", "apiVersion": "2018-11-30", "name": "[variables('managedIdentityName')]", "location": "[parameters('location')]"}, {"type": "Microsoft.OperationalInsights/workspaces", "apiVersion": "2022-10-01", "name": "[variables('logAnalyticsWorkspaceName')]", "location": "[parameters('location')]", "properties": {"sku": {"name": "PerGB2018"}, "retentionInDays": 30}}, {"type": "Microsoft.App/managedEnvironments", "apiVersion": "2022-06-01-preview", "name": "[variables('containerAppEnvironmentName')]", "location": "[parameters('location')]", "properties": {"appLogsConfiguration": {"destination": "log-analytics", "logAnalyticsConfiguration": {"customerId": "[reference(resourceId('Microsoft.OperationalInsights/workspaces', variables('logAnalyticsWorkspaceName'))).customerId]", "sharedKey": "[listKeys(resourceId('Microsoft.OperationalInsights/workspaces', variables('logAnalyticsWorkspaceName')), '2022-10-01').primarySharedKey]"}}}, "dependsOn": ["[resourceId('Microsoft.OperationalInsights/workspaces', variables('logAnalyticsWorkspaceName'))]"]}, {"type": "Microsoft.App/containerApps", "apiVersion": "2022-06-01-preview", "name": "[parameters('containerAppName')]", "location": "[parameters('location')]", "identity": {"type": "UserAssigned", "userAssignedIdentities": {"[resourceId('Microsoft.ManagedIdentity/userAssignedIdentities', variables('managedIdentityName'))]": {}}}, "properties": {"managedEnvironmentId": "[resourceId('Microsoft.App/managedEnvironments', variables('containerAppEnvironmentName'))]", "configuration": {"activeRevisionsMode": "Single", "registries": [{"server": "[concat(variables('lowerCaseRegistryName'), '.azurecr.io')]", "identity": "[resourceId('Microsoft.ManagedIdentity/userAssignedIdentities', variables('managedIdentityName'))]"}]}, "template": {"containers": [{"name": "[variables('containerName')]", "image": "[concat(variables('lowerCaseRegistryName'), '.azurecr.io/', parameters('containerImageName'))]", "resources": {"cpu": "1.0", "memory": "2Gi"}, "env": [{"name": "KEY_VAULT_NAME", "value": "[parameters('keyVaultName')]"}, {"name": "KEY_VAULT_SECRET_NAME", "value": "PUW-CREDENTIALS-8810"}, {"name": "STORAGE_ACCOUNT_NAME", "value": "[parameters('storageAccountName')]"}, {"name": "AZURE_CLIENT_ID", "value": "[reference(resourceId('Microsoft.ManagedIdentity/userAssignedIdentities', variables('managedIdentityName')), '2018-11-30').clientId]"}]}], "scale": {"minReplicas": 0, "maxReplicas": 1, "rules": [{"name": "cron-scale-rule", "custom": {"type": "cron", "metadata": {"timezone": "Europe/Berlin", "schedule": "[parameters('scheduleCron')]"}}}]}}}, "dependsOn": ["[resourceId('Microsoft.App/managedEnvironments', variables('containerAppEnvironmentName'))]", "[resourceId('Microsoft.ManagedIdentity/userAssignedIdentities', variables('managedIdentityName'))]"]}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[guid(resourceGroup().id, variables('managedIdentityName'), 'Key Vault Secrets User')]", "properties": {"roleDefinitionId": "[concat('/subscriptions/', subscription().subscriptionId, '/providers/Microsoft.Authorization/roleDefinitions/', '4633458b-17de-408a-b874-0445c86b69e6')]", "principalId": "[reference(resourceId('Microsoft.ManagedIdentity/userAssignedIdentities', variables('managedIdentityName')), '2018-11-30').principalId]", "principalType": "ServicePrincipal"}, "scope": "[resourceId('Microsoft.KeyVault/vaults', parameters('keyVaultName'))]", "dependsOn": ["[resourceId('Microsoft.ManagedIdentity/userAssignedIdentities', variables('managedIdentityName'))]"]}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[guid(resourceGroup().id, variables('managedIdentityName'), 'Storage Blob Data Contributor')]", "properties": {"roleDefinitionId": "[concat('/subscriptions/', subscription().subscriptionId, '/providers/Microsoft.Authorization/roleDefinitions/', 'ba92f5b4-2d11-453d-a403-e96b0029c9fe')]", "principalId": "[reference(resourceId('Microsoft.ManagedIdentity/userAssignedIdentities', variables('managedIdentityName')), '2018-11-30').principalId]", "principalType": "ServicePrincipal"}, "scope": "[resourceId('Microsoft.Storage/storageAccounts', parameters('storageAccountName'))]", "dependsOn": ["[resourceId('Microsoft.ManagedIdentity/userAssignedIdentities', variables('managedIdentityName'))]"]}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[guid(resourceGroup().id, variables('managedIdentityName'), 'AcrPull')]", "properties": {"roleDefinitionId": "[concat('/subscriptions/', subscription().subscriptionId, '/providers/Microsoft.Authorization/roleDefinitions/', '7f951dda-4ed3-4680-a7ca-43fe172d538d')]", "principalId": "[reference(resourceId('Microsoft.ManagedIdentity/userAssignedIdentities', variables('managedIdentityName')), '2018-11-30').principalId]", "principalType": "ServicePrincipal"}, "scope": "[resourceId('Microsoft.ContainerRegistry/registries', parameters('containerRegistryName'))]", "dependsOn": ["[resourceId('Microsoft.ManagedIdentity/userAssignedIdentities', variables('managedIdentityName'))]"]}], "outputs": {"managedIdentityClientId": {"type": "string", "value": "[reference(resourceId('Microsoft.ManagedIdentity/userAssignedIdentities', variables('managedIdentityName')), '2018-11-30').clientId]"}}}