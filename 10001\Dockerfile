# Use Python 3.11 with Playwright base image
FROM mcr.microsoft.com/playwright/python:v1.40.0-jammy

# Install timezone data and configure timezone non-interactively
ENV DEBIAN_FRONTEND=noninteractive
RUN apt-get update && apt-get install -y tzdata
# Set timezone to Europe/Berlin (Germany)
ENV TZ=Europe/Berlin
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
# Reset DEBIAN_FRONTEND after installation
ENV DEBIAN_FRONTEND=

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    iputils-ping \
    dnsutils \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install Playwright browsers
RUN playwright install chromium

# Copy the Python script
COPY sap_playwright_tester.py .

# Create necessary directories for screenshots and outputs
RUN mkdir -p /app/outputs /tmp/monitoring/img /tmp/monitoring/results

# Set environment variables
ENV AZURE_CLIENT_ID=""
ENV PYTHONUNBUFFERED=1
ENV TAKE_SCREENSHOT=true
ENV UPLOAD_TO_AZURE=true
ENV HEADLESS=true

# Set executable permissions
RUN chmod +x sap_playwright_tester.py

# Verify files are in place
RUN ls -la /app/

# Default command
CMD ["python", "sap_playwright_tester.py"]