{"network_info": {"container_info": {"hostname": "DESKTOP-JN20J6G", "platform": "Windows-10-10.0.26100-SP0", "python_version": "3.11.9", "timestamp": "2025-07-22T05:40:53.228900"}, "network_interfaces": {"LAN-Verbindung* 1": [{"type": "IPv4", "address": "************", "netmask": "***********", "broadcast": null}, {"type": "IPv6", "address": "fe80::c264:f12f:f15c:b139", "netmask": null}], "LAN-Verbindung* 2": [{"type": "IPv4", "address": "*************", "netmask": "***********", "broadcast": null}, {"type": "IPv6", "address": "fe80::54ec:cf72:ec76:c8b6", "netmask": null}], "WLAN": [{"type": "IPv4", "address": "**************", "netmask": "*************", "broadcast": null}, {"type": "IPv6", "address": "2a02:3100:8890:c800:62d1:76d4:b5c9:5aab", "netmask": null}, {"type": "IPv6", "address": "fdc7:8d5d:d1dc:0:622d:99a5:a4b:64a5", "netmask": null}, {"type": "IPv6", "address": "2a02:3100:8890:c800:1433:f916:57e8:17d7", "netmask": null}, {"type": "IPv6", "address": "fdc7:8d5d:d1dc:0:1433:f916:57e8:17d7", "netmask": null}, {"type": "IPv6", "address": "fe80::10d7:d50f:c436:e2ff", "netmask": null}], "Teredo Tunneling Pseudo-Interface": [{"type": "IPv6", "address": "2001:0:284a:364:287c:1dc2:6c5e:5b31", "netmask": null}, {"type": "IPv6", "address": "fe80::287c:1dc2:6c5e:5b31", "netmask": null}], "vEthernet (Default Switch)": [{"type": "IPv4", "address": "************", "netmask": "*************", "broadcast": null}, {"type": "IPv6", "address": "fe80::ce33:5173:6a6f:2e3a", "netmask": null}], "Loopback Pseudo-Interface 1": [{"type": "IPv4", "address": "127.0.0.1", "netmask": "*********", "broadcast": null}, {"type": "IPv6", "address": "::1", "netmask": null}]}, "dns_config": {"container_ip": "**************"}, "routing": {}}, "connectivity_tests": {"https://vhinoev1ci.rise.apps.eon.com:44300/sap/bc/gui/sap/its/webgui/#": {"url": "https://vhinoev1ci.rise.apps.eon.com:44300/sap/bc/gui/sap/its/webgui/#", "timestamp": "2025-07-22T05:40:53.253434", "tests": {"dns": {"status": "success", "ip": "**********", "reverse_dns": "vhinoev1ci.rise.apps.eon.com", "time_ms": 36.0}, "tcp_connectivity": {"status": "success", "time_ms": 14.0}, "ping": {"status": "success", "stats": {"packets_transmitted": 3, "packets_received": 3, "packet_loss": "Lost = 0 (0% loss)", "rtt_stats": "Minimum = 24ms, Maximum = 29ms, Average = 26ms"}}, "http": {"status": "success", "status_code": 401, "time_ms": 251.51, "content_length": 209, "headers": {"set-cookie": "SPNegoTokenRequested=2025-07-22%2005%3a41%3a00; path=/; secure; HttpOnly, sap-usercontext=sap-client=100; path=/", "content-type": "text/html; charset=utf-8", "content-length": "209", "www-authenticate": "Negotiate", "sap-server": "true", "sap-perf-fesrec": "4228.000000", "connection": "close"}, "encoding": "utf-8", "url_final": "https://vhinoev1ci.rise.apps.eon.com:44300/sap/bc/gui/sap/its/webgui/", "ssl": {"error": "'NoneType' object has no attribute 'sock'"}}}}, "https://oneb-central-dev.launchpad.cfapps.eu10.hana.ondemand.com/site?siteId=1af3caee-8117-49e3-b787-7ad74914b5cb": {"url": "https://oneb-central-dev.launchpad.cfapps.eu10.hana.ondemand.com/site?siteId=1af3caee-8117-49e3-b787-7ad74914b5cb", "timestamp": "2025-07-22T05:41:00.281632", "tests": {"dns": {"status": "success", "ip": "*************", "reverse_dns": "ec2-3-124-208-223.eu-central-1.compute.amazonaws.com", "time_ms": 43.01}, "tcp_connectivity": {"status": "success", "time_ms": 15.43}, "ping": {"status": "error", "error": ""}, "http": {"status": "success", "status_code": 200, "time_ms": 386.85, "content_length": 764, "headers": {"cache-control": "no-cache, no-store, must-revalidate", "content-security-policy": "script-src 'self' 'unsafe-inline'; frame-ancestors *", "content-type": "text/html", "date": "<PERSON><PERSON>, 22 Jul 2025 05:41:19 GMT", "set-cookie": "JSESSIONID=425bfb29-89c9-4318-8745-fdf19a0b9777; Path=/; HttpOnly; Secure; SameSite=None, __VCAP_ID__=9fdea0fb-af95-40e4-5c11-a49e; Path=/; HttpOnly; Secure; SameSite=None", "x-content-type-options": "nosniff", "x-request-id": "b3f938d1-71b3-4800-5ec5-59f52083c248", "x-vcap-request-id": "b3f938d1-71b3-4800-5ec5-59f52083c248", "transfer-encoding": "chunked", "strict-transport-security": "max-age=31536000; includeSubDomains; preload;", "connection": "close"}, "encoding": "ISO-8859-1", "url_final": "https://oneb-central-dev.launchpad.cfapps.eu10.hana.ondemand.com/site?siteId=1af3caee-8117-49e3-b787-7ad74914b5cb", "ssl": {"error": "'NoneType' object has no attribute 'sock'"}}}}, "https://www.google.com": {"url": "https://www.google.com", "timestamp": "2025-07-22T05:41:19.116036", "tests": {"dns": {"status": "success", "ip": "***************", "reverse_dns": "ham11s07-in-f4.1e100.net", "time_ms": 24.0}, "tcp_connectivity": {"status": "success", "time_ms": 14.95}, "ping": {"status": "success", "stats": {"packets_transmitted": 3, "packets_received": 3, "packet_loss": "Lost = 0 (0% loss)", "rtt_stats": "Minimum = 32ms, Maximum = 35ms, Average = 34ms"}}, "http": {"status": "success", "status_code": 200, "time_ms": 290.57, "content_length": 17955, "headers": {"Date": "<PERSON><PERSON>, 22 Jul 2025 05:41:26 GMT", "Expires": "-1", "Cache-Control": "private, max-age=0", "Content-Type": "text/html; charset=ISO-8859-1", "Content-Security-Policy-Report-Only": "object-src 'none';base-uri 'self';script-src 'nonce-7anEU6iUjcpM7TXnRYOH_A' 'strict-dynamic' 'report-sample' 'unsafe-eval' 'unsafe-inline' https: http:;report-uri https://csp.withgoogle.com/csp/gws/other-hp", "Accept-CH": "Sec-CH-Prefers-Color-Scheme", "P3P": "CP=\"This is not a P3P policy! See g.co/p3phelp for more info.\"", "Content-Encoding": "gzip", "Server": "gws", "X-XSS-Protection": "0", "X-Frame-Options": "SAMEORIGIN", "Set-Cookie": "SOCS=CAAaBgiAqfvDBg; expires=Fri, 21-Aug-2026 05:41:26 GMT; path=/; domain=.google.com; Secure; SameSite=lax, AEC=AVh_V2jOxH7tIeLaSZg9g5mhD0UIoEAzZ8zDaDjV7sfCbhH6fhEt4701tA; expires=Sun, 18-Jan-2026 05:41:26 GMT; path=/; domain=.google.com; Secure; HttpOnly; SameSite=lax, __Secure-ENID=28.SE=MAOkxeDznLGqdEBc_fy_VujWcF0b58Z9TPCo5AJlyLhUQ7fPHTNcNouIuFbBf5_F9f1LPsF_guUC4PrAUPWweEVEJLlQVM8HkWLZLGhpvDrGkSKDA0uAjcB6h9OHqswr5_5lVLGfgSSo4VD_M3EJ4SWf6EVttZMBHqSkwi6qv_i6PQ0GAuxjASmL3rpQzXvJ6_Nm2Cf2eiKKy0T9DHRiRcRNJL4; expires=Fri, 21-Aug-2026 21:59:44 GMT; path=/; domain=.google.com; Secure; HttpOnly; SameSite=lax", "Alt-Svc": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000", "Connection": "close", "Transfer-Encoding": "chunked"}, "encoding": "ISO-8859-1", "url_final": "https://www.google.com/", "ssl": {"error": "'NoneType' object has no attribute 'sock'"}}}}}}