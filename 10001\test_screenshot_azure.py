#!/usr/bin/env python3

"""
Test script for SAP Playwright Tester with Azure screenshot upload
This script tests the screenshot functionality without running the full test
"""

import asyncio
import os
import sys
from datetime import datetime
from playwright.async_api import async_playwright

# Add the current directory to Python path to import our module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import our tester
from sap_playwright_tester import SAPPlaywrightTester

async def test_screenshot_functionality():
    """Test the screenshot functionality"""
    print("🧪 Testing SAP Playwright Tester Screenshot Functionality")
    print("=" * 60)
    
    # Create tester instance
    tester = SAPPlaywrightTester()
    
    # Test Azure initialization
    print(f"🔧 Azure available: {tester.azure_initialized if hasattr(tester, 'azure_initialized') else 'Not checked'}")
    
    # Launch browser for testing
    async with async_playwright() as p:
        print("🌐 Launching browser...")
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        # Navigate to a simple test page
        print("📄 Navigating to test page...")
        await page.goto("https://httpbin.org/html")
        
        # Take a test screenshot
        print("📸 Taking test screenshot...")
        screenshot_path = await tester._take_screenshot(page, "TEST")
        
        if screenshot_path:
            print(f"✅ Screenshot test successful!")
            print(f"   Local path: {screenshot_path}")
            
            # Check if file exists
            if os.path.exists(screenshot_path):
                file_size = os.path.getsize(screenshot_path)
                print(f"   File size: {file_size:,} bytes")
            
        else:
            print("❌ Screenshot test failed")
        
        # Show results
        print("\n📊 Test Results:")
        print(f"   Screenshots taken: {len(tester.results.get('screenshots', []))}")
        for screenshot in tester.results.get('screenshots', []):
            status = "☁️ Azure" if screenshot.get('azure_uploaded', False) else "💾 Local only"
            print(f"   • {screenshot['filename']} ({status})")
        
        await browser.close()
    
    print("\n🏁 Test completed!")

if __name__ == "__main__":
    asyncio.run(test_screenshot_functionality())
