{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"containerAppName": {"value": "sap-playwright-enterprise-10001"}, "location": {"value": "germanywestcentral"}, "keyVaultName": {"value": "b2smepcw-kv-12345"}, "storageAccountName": {"value": "b2smepcwsa12345"}, "containerRegistryName": {"value": "scriptmonregistry2025"}, "containerImageName": {"value": "sap-playwright-enterprise:latest"}, "scheduleCron": {"value": "*/5 * * * *"}, "sapTargetUrl": {"value": "https://oneb-central-dev.launchpad.cfapps.eu10.hana.ondemand.com/site?siteId=1af3caee-8117-49e3-b787-7ad74914b5cb"}, "splunkUrl": {"value": "https://your-splunk.company.com:8088/services/collector"}, "splunkToken": {"value": "your-splunk-hec-token-here"}}}