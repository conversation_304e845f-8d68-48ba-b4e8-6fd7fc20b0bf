#!/bin/bash

# Configuration
RESOURCE_GROUP="RG-EDGMON-RUN"
KEYVAULT_NAME="scriptsmonitoring2025"
ACR_NAME="scriptmonregistry2025"
STORAGE_ACCOUNT="scriptsmonitoring2025"
MANAGED_IDENTITY="scriptmonregistry2025-identity"
CONTAINER_APP_ENV="managedEnvironment-RGEDGMONRUN-b130"
JOB_NAME="eon-monitoring-3111-job"
IMAGE_NAME="eon-monitoring-3111"
IMAGE_TAG="latest"

echo "🚀 Création du Job Container App pour EON Monitoring"
echo "=================================================="

# Vérifier si connecté à Azure
echo "📋 Vérification de la connexion Azure..."
az account show > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ Veuillez vous connecter à Azure avec 'az login'"
    exit 1
fi

# Récupérer les informations nécessaires
SUBSCRIPTION_ID=$(az account show --query id --output tsv)
CLIENT_ID=$(az identity show --resource-group $RESOURCE_GROUP --name $MANAGED_IDENTITY --query clientId --output tsv 2>/dev/null)
IDENTITY_ID="/subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.ManagedIdentity/userAssignedIdentities/$MANAGED_IDENTITY"

if [ -z "$CLIENT_ID" ]; then
    echo "❌ Managed Identity '$MANAGED_IDENTITY' non trouvée dans le resource group '$RESOURCE_GROUP'"
    echo "💡 Vérifiez que les noms des ressources sont corrects"
    exit 1
fi

echo "✅ Configuration trouvée:"
echo "  - Subscription ID: $SUBSCRIPTION_ID"
echo "  - Resource Group: $RESOURCE_GROUP"
echo "  - Managed Identity: $MANAGED_IDENTITY"
echo "  - Client ID: $CLIENT_ID"
echo "  - Job Name: $JOB_NAME"
echo "  - Image: $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG"

# Vérifier que l'image existe dans ACR
echo ""
echo "🔍 Vérification de l'image dans ACR..."
IMAGE_EXISTS=$(az acr repository show --name $ACR_NAME --image $IMAGE_NAME:$IMAGE_TAG --query name --output tsv 2>/dev/null)
if [ -z "$IMAGE_EXISTS" ]; then
    echo "⚠️  Image '$IMAGE_NAME:$IMAGE_TAG' non trouvée dans ACR '$ACR_NAME'"
    echo "💡 Vous devez d'abord construire et pousser l'image:"
    echo "   docker build -t $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG ."
    echo "   docker push $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG"
    read -p "Voulez-vous continuer quand même ? (y/N): " continue_anyway
    if [[ ! "$continue_anyway" =~ ^[Yy]$ ]]; then
        exit 1
    fi
else
    echo "✅ Image trouvée dans ACR"
fi

# Créer le stockage Azure Files pour l'environnement (si pas déjà fait)
echo ""
echo "💾 Configuration du stockage Azure Files..."
STORAGE_KEY=$(az storage account keys list --resource-group $RESOURCE_GROUP --account-name $STORAGE_ACCOUNT --query "[0].value" --output tsv 2>/dev/null)

if [ -z "$STORAGE_KEY" ]; then
    echo "❌ Impossible de récupérer la clé du storage account '$STORAGE_ACCOUNT'"
    exit 1
fi

# Vérifier si le stockage est déjà configuré dans l'environnement
STORAGE_EXISTS=$(az containerapp env storage show --name $CONTAINER_APP_ENV --resource-group $RESOURCE_GROUP --storage-name "script-monitoring-storage" --query name --output tsv 2>/dev/null)

if [ -z "$STORAGE_EXISTS" ]; then
    echo "🔧 Configuration du stockage dans l'environnement Container Apps..."
    az containerapp env storage set \
        --name $CONTAINER_APP_ENV \
        --resource-group $RESOURCE_GROUP \
        --storage-name "script-monitoring-storage" \
        --azure-file-account-name $STORAGE_ACCOUNT \
        --azure-file-account-key $STORAGE_KEY \
        --azure-file-share-name "script-monitoring" \
        --access-mode ReadWrite
    
    if [ $? -eq 0 ]; then
        echo "✅ Stockage configuré"
    else
        echo "❌ Erreur lors de la configuration du stockage"
        exit 1
    fi
else
    echo "✅ Stockage déjà configuré"
fi

# Vérifier si le job existe déjà
echo ""
echo "🔍 Vérification si le job existe déjà..."
JOB_EXISTS=$(az containerapp job show --name $JOB_NAME --resource-group $RESOURCE_GROUP --query name --output tsv 2>/dev/null)

if [ ! -z "$JOB_EXISTS" ]; then
    echo "⚠️  Le job '$JOB_NAME' existe déjà"
    read -p "Voulez-vous le supprimer et le recréer ? (y/N): " recreate_job
    if [[ "$recreate_job" =~ ^[Yy]$ ]]; then
        echo "🗑️  Suppression du job existant..."
        az containerapp job delete --name $JOB_NAME --resource-group $RESOURCE_GROUP --yes
        echo "✅ Job supprimé"
    else
        echo "ℹ️  Mise à jour du job existant..."
        az containerapp job update \
            --name $JOB_NAME \
            --resource-group $RESOURCE_GROUP \
            --image $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG
        
        if [ $? -eq 0 ]; then
            echo "✅ Job mis à jour avec succès"
            echo ""
            echo "🎉 Job prêt !"
            echo "📊 Commandes utiles:"
            echo "  - Démarrer manuellement: az containerapp job start --name $JOB_NAME --resource-group $RESOURCE_GROUP"
            echo "  - Voir les exécutions: az containerapp job execution list --name $JOB_NAME --resource-group $RESOURCE_GROUP"
            echo "  - Voir les logs: ./monitoring-commands.sh"
            exit 0
        else
            echo "❌ Erreur lors de la mise à jour du job"
            exit 1
        fi
    fi
fi

# Créer le Container App Job
echo ""
echo "🔧 Création du Container App Job..."
az containerapp job create \
    --name $JOB_NAME \
    --resource-group $RESOURCE_GROUP \
    --environment $CONTAINER_APP_ENV \
    --trigger-type "Schedule" \
    --cron-expression "*/5 * * * *" \
    --image $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG \
    --user-assigned $IDENTITY_ID \
    --registry-server $ACR_NAME.azurecr.io \
    --registry-identity $IDENTITY_ID \
    --env-vars AZURE_CLIENT_ID=$CLIENT_ID \
    --cpu 1.0 \
    --memory 2.0Gi \
    --parallelism 1 \
    --replica-completion-count 1 \
    --replica-timeout 3600 \
    --replica-retry-limit 2

if [ $? -ne 0 ]; then
    echo "❌ Erreur lors de la création du job"
    exit 1
fi

echo "✅ Job créé avec succès !"

# Ajouter le volume Azure Files
echo ""
echo "📁 Configuration du volume Azure Files..."
az containerapp job update \
    --name $JOB_NAME \
    --resource-group $RESOURCE_GROUP \
    --set properties.template.volumes='[
        {
            "name": "azure-files-volume",
            "storageType": "AzureFile",
            "storageName": "script-monitoring-storage"
        }
    ]' \
    --set properties.template.containers[0].volumeMounts='[
        {
            "volumeName": "azure-files-volume",
            "mountPath": "/mnt/monitoring"
        }
    ]'

if [ $? -eq 0 ]; then
    echo "✅ Volume Azure Files configuré"
else
    echo "⚠️  Erreur lors de la configuration du volume (le job fonctionne quand même)"
fi

echo ""
echo "🎉 CRÉATION TERMINÉE AVEC SUCCÈS !"
echo "================================="
echo ""
echo "📊 Informations du Job:"
echo "  - Nom: $JOB_NAME"
echo "  - Schedule: Toutes les 30 minutes (0 */30 * * * *)"
echo "  - Timeout: 1 heure"
echo "  - Retry: 2 tentatives"
echo "  - Image: $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG"
echo ""
echo "🔍 Commandes utiles:"
echo "  - Démarrer manuellement:"
echo "    az containerapp job start --name $JOB_NAME --resource-group $RESOURCE_GROUP"
echo ""
echo "  - Voir les exécutions:"
echo "    az containerapp job execution list --name $JOB_NAME --resource-group $RESOURCE_GROUP"
echo ""
echo "  - Voir les logs d'une exécution:"
echo "    az containerapp job logs show --name $JOB_NAME --resource-group $RESOURCE_GROUP --execution-name EXECUTION_NAME"
echo ""
echo "  - Voir le statut du job:"
echo "    az containerapp job show --name $JOB_NAME --resource-group $RESOURCE_GROUP"
echo ""
echo "  - Utiliser le script de monitoring:"
echo "    ./monitoring-commands.sh"
echo ""
echo "⚠️  Changements de schedule:"
echo "  - Modifier la fréquence:"
echo "    az containerapp job update --name $JOB_NAME --resource-group $RESOURCE_GROUP --cron-expression 'NOUVELLE_EXPRESSION'"
echo ""
echo "  - Exemples de cron:"
echo "    * Toutes les 15 minutes: '0 */15 * * * *'"
echo "    * Toutes les heures: '0 0 * * * *'"
echo "    * Tous les jours à 8h: '0 0 8 * * *'"
echo "    * Du lundi au vendredi à 9h: '0 0 9 * * 1-5'"
echo ""
echo "🚀 Le job est maintenant prêt et s'exécutera automatiquement toutes les 30 minutes !"