# SAP Playwright Tester with Azure Screenshot Upload

## 📸 New Features Added

This enhanced version of the SAP Playwright Tester now includes automatic screenshot capture and upload to Azure Storage, similar to the functionality in script 3111.

### Key Enhancements

1. **Automatic Screenshot Capture**
   - Screenshots are taken after navigation to the target URL
   - Final screenshot is captured at the end of the test
   - Screenshots are saved with timestamps and descriptive names

2. **Azure Storage Integration**
   - Screenshots are automatically uploaded to Azure File Share
   - Uses the same Azure infrastructure as script 3111
   - Fallback to local storage if Azure is unavailable

3. **Enhanced Logging**
   - Detailed progress tracking for screenshot operations
   - Azure upload status reporting
   - File size and location information

## 🔧 Configuration

### Environment Variables

The following new environment variables are available:

```bash
# Azure Storage Configuration
AZURE_KEYVAULT_NAME=scriptsmonitoring2025
AZURE_STORAGE_ACCOUNT=scriptsmonitoring2025
AZURE_FILE_SHARE=script-monitoring
AZURE_CONTAINER_NAME=10001
AZURE_SAS_TOKEN=<your-sas-token>

# Screenshot Configuration
LOCAL_IMG_PATH=/tmp/monitoring/img
UPLOAD_TO_AZURE=true
TAKE_SCREENSHOT=true
```

### Azure Infrastructure

The script uses the same Azure infrastructure as script 3111:

- **Key Vault**: `scriptsmonitoring2025`
- **Storage Account**: `scriptsmonitoring2025`
- **File Share**: `script-monitoring`
- **Container**: `10001`

## 📁 File Structure

Screenshots are organized as follows:

```
Azure File Share: script-monitoring/
└── 10001/
    └── screenshots/
        ├── 10001_YYYYMMDD-HHMMSS_AFTER_NAVIGATION.png
        └── 10001_YYYYMMDD-HHMMSS_FINAL_PAGE.png
```

Local storage (fallback):
```
/tmp/monitoring/img/
├── 10001_YYYYMMDD-HHMMSS_AFTER_NAVIGATION.png
└── 10001_YYYYMMDD-HHMMSS_FINAL_PAGE.png
```

## 🚀 Deployment

### Prerequisites

1. Azure CLI installed and logged in
2. Docker installed
3. Access to the existing Azure infrastructure

### Quick Deployment

```bash
# Make the deployment script executable
chmod +x deploy.sh

# Run deployment
./deploy.sh
```

### Manual Testing

To test the screenshot functionality locally:

```bash
# Install dependencies
pip install -r requirements.txt

# Install Playwright browsers
playwright install chromium

# Run the test script
python test_screenshot_azure.py
```

## 📊 Output

The enhanced script now includes screenshot information in the JSON output:

```json
{
  "test_info": {
    "configuration": {
      "screenshot_enabled": true,
      "azure_upload_enabled": true
    }
  },
  "screenshots": [
    {
      "filename": "10001_********-143022_AFTER_NAVIGATION.png",
      "local_path": "/tmp/monitoring/img/10001_********-143022_AFTER_NAVIGATION.png",
      "step_name": "AFTER_NAVIGATION",
      "timestamp": "********-143022",
      "url": "https://example.com",
      "title": "Page Title",
      "azure_uploaded": true,
      "azure_path": "10001/screenshots/10001_********-143022_AFTER_NAVIGATION.png"
    }
  ]
}
```

## 🔍 Monitoring

### Container App Logs

```bash
az containerapp logs show --name sap-monitoring-10001-app --resource-group RG-EDGMON-RUN --follow
```

### Azure Storage

Screenshots can be viewed in the Azure portal:
1. Navigate to Storage Account: `scriptsmonitoring2025`
2. Go to File shares → `script-monitoring`
3. Browse to `10001/screenshots/`

## 🛠️ Troubleshooting

### Common Issues

1. **Azure Authentication Failed**
   - Ensure the managed identity has access to Key Vault and Storage
   - Check that the SAS token is valid and not expired

2. **Screenshots Not Uploading**
   - Verify Azure credentials and permissions
   - Check network connectivity to Azure services
   - Screenshots will still be saved locally as fallback

3. **Large Screenshot Files**
   - Screenshots are taken as full-page captures
   - File sizes typically range from 100KB to 2MB depending on page content

### Debug Mode

Set environment variables for debugging:

```bash
export HEADLESS=false  # Run browser in visible mode
export TAKE_SCREENSHOT=true
export UPLOAD_TO_AZURE=false  # Test local screenshots only
```

## 📝 Changes Made

### Files Modified

1. **sap_playwright_tester.py**
   - Added Azure libraries import with fallback
   - Added Azure configuration variables
   - Enhanced SAPPlaywrightTester class with Azure functionality
   - Added `upload_to_azure_storage()` method
   - Enhanced `_take_screenshot()` method with Azure upload
   - Added screenshot tracking in results

2. **requirements.txt**
   - Added Azure SDK dependencies:
     - azure-keyvault-secrets==4.7.0
     - azure-identity==1.15.0
     - azure-storage-file-share==12.14.1

3. **Dockerfile**
   - Added timezone configuration
   - Created necessary directories for screenshots
   - Added Azure-related environment variables

### Files Added

1. **deploy.sh** - Azure deployment script
2. **test_screenshot_azure.py** - Local testing script
3. **README-AZURE-SCREENSHOTS.md** - This documentation

## 🔗 Integration with Script 3111

This implementation follows the same patterns and uses the same Azure infrastructure as script 3111, ensuring consistency across the monitoring platform.
