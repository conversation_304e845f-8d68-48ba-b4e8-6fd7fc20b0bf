from playwright.sync_api import Playwright, sync_playwright
import os
import sys
import time
from datetime import date, datetime
import uuid
import json
import logging
import ssl
import urllib3
import requests
# import pyodbc
import pymssql
import datetime as dt

# Désactiver la vérification SSL au niveau système
os.environ['PYTHONHTTPSVERIFY'] = '0'
os.environ['CURL_CA_BUNDLE'] = ''
os.environ['REQUESTS_CA_BUNDLE'] = ''

# Only import Azure libraries, no pyodbc
try:
    from azure.keyvault.secrets import SecretClient
    from azure.identity import DefaultAzureCredential
    from azure.storage.fileshare import ShareFileClient
    print("✅ Azure libraries imported successfully")
except ImportError as e:
    print(f"❌ Azure library import error: {e}")
    sys.exit(1)

# Désactiver les avertissements SSL et la vérification des certificats
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Désactiver les logs des bibliothèques Azure SDK
logging.getLogger('azure').setLevel(logging.ERROR)
logging.getLogger('azure.identity').setLevel(logging.ERROR)
logging.getLogger('azure.core').setLevel(logging.ERROR)
logging.getLogger('azure.keyvault').setLevel(logging.ERROR)
logging.getLogger('azure.storage').setLevel(logging.ERROR)
logging.getLogger('azure.storage.fileshare').setLevel(logging.ERROR)  # Spécifiquement pour File Share
logging.getLogger('azure.core.pipeline').setLevel(logging.ERROR)      # Pour les pipelines HTTP
logging.getLogger('azure.core.pipeline.policies').setLevel(logging.ERROR)  # Pour les politiques de pipeline
logging.getLogger('msal').setLevel(logging.ERROR)
logging.getLogger('urllib3').setLevel(logging.ERROR)
logging.getLogger('requests').setLevel(logging.ERROR)

# Créer un logger spécifique pour la DB avec plus de détails
db_logger = logging.getLogger('database')
db_logger.setLevel(logging.DEBUG)
db_handler = logging.StreamHandler()
db_formatter = logging.Formatter('%(asctime)s - DB - %(levelname)s - %(message)s')
db_handler.setFormatter(db_formatter)
db_logger.addHandler(db_handler)
db_logger.propagate = False  # Éviter la duplication des logs

class SimpleEonMonitoring:
    def __init__(self):
        # Configuration Azure
        self.keyvault_name = "scriptsmonitoring2025"
        self.keyvault_url = f"https://{self.keyvault_name}.vault.azure.net/"
        self.storage_account_name = "scriptsmonitoring2025"
        self.file_share_name = "script-monitoring"
        self.container_name = "3111"

        # SAS Token avec toutes les permissions
        self.sas_token = "sv=2024-11-04&ss=bfqt&srt=sco&sp=rwlacupiytfx&se=2099-05-27T16:12:28Z&st=2025-05-27T08:12:28Z&spr=https&sig=aC8CDIMg0AP4NbymnSwflWBaGKG7sFpswl%2FcslzuqPY%3D"

        # Configuration Splunk HEC
        self.splunk_hec_url = "https://splunk-hec.eon.com:443/services/collector/event"
        self.splunk_token = "bbab61ff-f874-4e71-8403-257370da6485"
        self.splunk_sourcetype = "http_eek_ng_monitoring"
        self.splunk_index = "idx_eek_robots"
        self.splunk_host = "azure_mon_3111"

        # Initialisation des clients Azure
        try:
            self.credential = DefaultAzureCredential()
            self.secret_client = SecretClient(vault_url=self.keyvault_url, credential=self.credential)
            logger.info("✅ Azure clients initialized successfully")
            
            # Récupérer les credentials depuis Key Vault
            self.email = self.get_secret("eon-email")
            self.password = self.get_secret("eon-password")
            
            if not self.email or not self.password:
                logger.error("❌ Unable to retrieve credentials from Key Vault")
                raise Exception("Missing credentials")
            
            logger.info(f"✅ Credentials retrieved for email: {self.email[:5]}***")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Azure clients: {str(e)}")
            raise

        # Chemins locaux
        self.local_results_path = "/tmp/monitoring/results"
        self.local_img_path = "/tmp/monitoring/img"

        # Créer les répertoires locaux
        os.makedirs(self.local_results_path, exist_ok=True)
        os.makedirs(self.local_img_path, exist_ok=True)

        logger.info("✅ Simple EON Monitoring initialized")

    def get_secret(self, secret_name):
        """Récupère un secret depuis Azure Key Vault"""
        try:
            retrieved_secret = self.secret_client.get_secret(secret_name)
            logger.info(f"✅ Secret {secret_name} retrieved successfully")
            return retrieved_secret.value
        except Exception as e:
            logger.error(f"❌ Error retrieving secret {secret_name}: {str(e)}")
            return None

    def upload_to_azure_storage(self, local_file_path, remote_file_path):
        """Upload un fichier vers Azure File Share avec SAS Token"""
        try:
            # Utiliser SAS Token au lieu de DefaultAzureCredential pour éviter les problèmes SSL
            file_client = ShareFileClient(
                account_url=f"https://{self.storage_account_name}.file.core.windows.net",
                share_name=self.file_share_name,
                file_path=remote_file_path,
                credential=self.sas_token,
                connection_verify=False  # Désactiver la vérification SSL
            )

            with open(local_file_path, "rb") as data:
                file_client.upload_file(data)

            logger.info(f"✅ File uploaded successfully: {remote_file_path}")
            return True
        except Exception as e:
            logger.error(f"❌ Error uploading file {local_file_path}: {str(e)}")
            return False

    def send_to_splunk(self, event_data):
        """Envoie les données d'événement vers Splunk HEC"""
        try:
            # Créer le timestamp epoch avec millisecondes à partir du timestamp ISO
            timestamp_iso = event_data["timestamp"]
            
            # Si le timestamp est une chaîne ISO
            if isinstance(timestamp_iso, str):
                timestamp_dt = datetime.fromisoformat(timestamp_iso.replace('Z', '+00:00'))
            else:
                # Si c'est déjà un objet datetime
                timestamp_dt = timestamp_iso
            
            epoch_time = timestamp_dt.timestamp()  # Garder les millisecondes (float)

            # Construire l'événement Splunk au format requis
            splunk_event = {
                "time": epoch_time,
                "sourcetype": self.splunk_sourcetype,
                "event": {
                    "timestamp": event_data["timestamp"],
                    "date": event_data["date"],
                    "event_time": event_data["time"],
                    "script_id": event_data["script_id"],
                    "metrics": event_data["metrics"],
                    "total_time": event_data["total_time"],
                    "success": event_data["success"],
                    "type": "New generation monitoring"
                }
            }

            # Headers pour Splunk HEC
            headers = {
                "Authorization": f"Splunk {self.splunk_token}",
                "Content-Type": "application/json"
            }

            logger.info("📦 Payload:")
            payload_str = json.dumps(splunk_event, indent=2)
            logger.info(f"{payload_str}")
            logger.info("=" * 60)

            # Envoyer l'événement à Splunk
            logger.info("🚀 Sending request to Splunk HEC...")
            response = requests.post(
                self.splunk_hec_url,
                headers=headers,
                json=splunk_event,
                verify=False,  # Désactiver la vérification SSL
                timeout=30
            )

            # Log détaillé de la réponse Splunk HEC
            logger.info("=" * 60)
            logger.info("📥 SPLUNK HEC RESPONSE DETAILS")
            logger.info("=" * 60)
            logger.info(f"📊 Status Code: {response.status_code}")
            logger.info("📋 Response Headers:")
            for key, value in response.headers.items():
                logger.info(f"    '{key}': '{value}'")

            logger.info(f"📄 Response Body:")
            try:
                if response.text:
                    # Essayer de parser comme JSON pour un affichage formaté
                    try:
                        response_json = response.json()
                        logger.info(json.dumps(response_json, indent=2))
                    except:
                        logger.info(f"{response.text}")
                else:
                    logger.info("(Empty response body)")
            except Exception as resp_error:
                logger.warning(f"⚠️ Could not read response body: {str(resp_error)}")

            logger.info("=" * 60)

            if response.status_code == 200:
                logger.info("✅ Event sent to Splunk successfully")
                return True
            else:
                logger.error(f"❌ Splunk HEC error: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"❌ Error sending event to Splunk: {str(e)}")
            return False

    def send_to_new_relic(self, event_data):
        """Envoie les données d'événement vers New Relic Logs API"""
        try:
            # New Relic Log API settings
            new_relic_log_api_url = 'https://log-api.eu.newrelic.com/log/v1'
            new_relic_api_key = 'eu01xx0a982ff080c0b184b6137750a8FFFFNRAL'

            # Convertir le timestamp ISO en epoch
            from datetime import datetime
            import time
            
            # Si le timestamp est déjà au format ISO
            if isinstance(event_data["timestamp"], str):
                dt_obj = datetime.fromisoformat(event_data["timestamp"].replace('Z', '+00:00'))
                epoch_time = str(int(dt_obj.timestamp()))
            else:
                # Si c'est déjà un objet datetime
                epoch_time = str(int(time.time()))

            # Préparer les données pour New Relic avec le format spécifié
            new_relic_payload = {
                "timestamp": epoch_time,
                "message": f"EON Monitoring Event - script {event_data['script_id']}",
                "attributes": {
                    "date": event_data["date"],
                    "event_time": event_data["time"],
                    "script_id": event_data["script_id"],
                    "metrics": event_data["metrics"],
                    "total_time": event_data["total_time"],
                    "success": event_data["success"],
                    "type": "New generation monitoring",
                    "source": "azure_mon_3111"
                }
            }

            # Headers pour New Relic
            headers = {
                "Api-Key": new_relic_api_key,
                "Content-Type": "application/json"
            }

            logger.info("📦 Payload:")
            payload_str = json.dumps(new_relic_payload, indent=2)
            logger.info(f"{payload_str}")
            logger.info("=" * 60)

            # Envoyer les données à New Relic
            logger.info("🚀 Sending request to New Relic Logs API...")
            response = requests.post(
                new_relic_log_api_url,
                headers=headers,
                json=new_relic_payload,
                verify=False,  # Désactiver la vérification SSL
                timeout=30
            )

            # Log détaillé de la réponse New Relic
            logger.info("=" * 60)
            logger.info("📥 NEW RELIC API RESPONSE DETAILS")
            logger.info("=" * 60)
            logger.info(f"📊 Status Code: {response.status_code}")
            logger.info("📋 Response Headers:")
            for key, value in response.headers.items():
                logger.info(f"    '{key}': '{value}'")

            logger.info(f"📄 Response Body:")
            try:
                if response.text:
                    try:
                        response_json = response.json()
                        logger.info(json.dumps(response_json, indent=2))
                    except:
                        logger.info(f"{response.text}")
                else:
                    logger.info("(Empty response body)")
            except Exception as resp_error:
                logger.warning(f"⚠️ Could not read response body: {str(resp_error)}")

            logger.info("=" * 60)

            if response.status_code in [200, 202]:  # New Relic renvoie généralement 202 Accepted
                logger.info("✅ Event sent to New Relic successfully")
                return True
            else:
                logger.error(f"❌ New Relic API error: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"❌ Error sending event to New Relic: {str(e)}")
            return False

    def log_results(self, metrics_data):
        """Log les résultats localement et sur Azure (CSV + JSON) et envoie aux services externes"""
        try:
            # Configurer le fuseau horaire explicitement
            import pytz
            local_tz = pytz.timezone('Europe/Berlin')  # Fuseau horaire allemand
            
            # Obtenir l'heure locale actuelle
            now_utc = datetime.now(pytz.UTC)
            now_local = now_utc.astimezone(local_tz)
            
            # 1. CSV Log (format original)
            csv_file_path = os.path.join(self.local_results_path, "3111_RTR.csv")

            # Créer le header CSV si nécessaire
            if not os.path.exists(csv_file_path):
                with open(csv_file_path, 'w') as f:
                    header = "SCRIPT_ID;SCRIPT_DATE;SCRIPT_START_TIME;LOAD_LOGIN_PAGE;LOGIN;METER_READING;ABSCHLAG;LOGOUT;\n"
                    f.write(header)
                logger.info("✅ CSV header created")

            # IMPORTANT: metrics_data contient DÉJÀ des millisecondes, PAS de conversion nécessaire
            # Log pour déboguer les valeurs
            logger.info("🔍 DEBUG - Metrics values (already in milliseconds):")
            logger.info(f"  load_login_page: {metrics_data['load_login_page']} ms")
            logger.info(f"  login: {metrics_data['login']} ms")
            logger.info(f"  meter_reading: {metrics_data['meter_reading']} ms")
            logger.info(f"  abschlag: {metrics_data['abschlag']} ms")
            logger.info(f"  logout: {metrics_data['logout']} ms")

            # Ajouter les données CSV
            with open(csv_file_path, 'a') as f:
                csv_line = f"3111;{now_local.strftime('%d.%m.%Y')};{now_local.strftime('%H:%M:%S')};{metrics_data['load_login_page']};{metrics_data['login']};{metrics_data['meter_reading']};{metrics_data['abschlag']};{metrics_data['logout']};\n"
                f.write(csv_line)

            logger.info(f"✅ Data written to local CSV: {csv_line.strip()}")

            # 2. JSON Log (plus détaillé)
            json_file_path = os.path.join(self.local_results_path, "3111_detailed.json")

            detailed_data = {
                "timestamp": now_local.isoformat(),
                "date": now_local.strftime('%d.%m.%Y'),
                "time": now_local.strftime('%H:%M:%S'),
                "script_id": "3111",
                "metrics": metrics_data,  # Utiliser directement les valeurs en ms
                "total_time": sum([v for v in metrics_data.values() if v > 0]),
                "success": metrics_data['login'] > 0,
                "type": "New generation monitoring"
            }

            # Lire les données existantes ou créer un nouveau fichier
            json_data = []
            if os.path.exists(json_file_path):
                try:
                    with open(json_file_path, 'r') as f:
                        json_data = json.load(f)
                except:
                    json_data = []

            json_data.append(detailed_data)

            # Garder seulement les 100 dernières entrées
            json_data = json_data[-100:]

            with open(json_file_path, 'w') as f:
                json.dump(json_data, f, indent=2)

            logger.info("✅ Data written to JSON log")

            # 3. Upload vers Azure File Share
            csv_remote_path = f"{self.container_name}/3111_RTR.csv"
            json_remote_path = f"{self.container_name}/3111_detailed.json"

            csv_uploaded = self.upload_to_azure_storage(csv_file_path, csv_remote_path)
            json_uploaded = self.upload_to_azure_storage(json_file_path, json_remote_path)

            if csv_uploaded and json_uploaded:
                logger.info("✅ All results successfully uploaded to Azure Storage")
            else:
                logger.warning("⚠️ Some files failed to upload to Azure Storage")

            # 4. Envoyer l'événement vers Splunk HEC
            logger.info("📡 Sending event to Splunk HEC...")
            splunk_sent = self.send_to_splunk(detailed_data)

            if splunk_sent:
                logger.info("✅ Event successfully sent to Splunk")
            else:
                logger.warning("⚠️ Failed to send event to Splunk")
            
            # 5. Envoyer l'événement vers New Relic
            logger.info("📡 Sending event to New Relic...")
            new_relic_sent = self.send_to_new_relic(detailed_data)
            
            if new_relic_sent:
                logger.info("✅ Event successfully sent to New Relic")
            else:
                logger.warning("⚠️ Failed to send event to New Relic")

            # 6. Log en DB uniquement si le login a réussi
            if metrics_data['login'] > 0:
                logger.info("📊 Logging to database...")
                try:
                    # Utiliser directement la valeur en ms
                    self.logdb(str(metrics_data['login']))
                    logger.info("✅ Database logging completed")
                except Exception as db_error:
                    logger.error(f"❌ Database logging failed: {str(db_error)}")
            else:
                logger.warning("⚠️ Login failed, skipping database logging")

            return True

        except Exception as e:
            logger.error(f"❌ Error in log_results function: {str(e)}")
            return False

    def format_ms_for_display(self, ms_value):
        """Formate une valeur en millisecondes pour l'affichage"""
        if ms_value <= 0:
            return "0"
        return str(ms_value)

    def take_screenshot(self, page, step_name):
        """Prend un screenshot et l'upload vers Azure"""
        try:
            # Debug: Log current URL and page title
            current_url = page.url
            try:
                page_title = page.title()
            except:
                page_title = "Unknown"

            logger.info(f"📸 Taking screenshot for {step_name}")
            logger.info(f"📍 URL: {current_url}")
            logger.info(f"📄 Title: {page_title}")

            # Attendre que la page soit stable avant le screenshot
            try:
                logger.info("⏳ Waiting for page to be stable...")
                page.wait_for_load_state("networkidle", timeout=10000)
                logger.info("✅ Page networkidle state reached")

                # Attendre encore plus pour le contenu dynamique
                logger.info("⏳ Waiting additional time for dynamic content...")
                page.wait_for_timeout(5000)
                logger.info("✅ Additional wait completed")

                # Vérifier si il y a du contenu visible
                try:
                    body_content = page.locator("body").inner_text()
                    if len(body_content.strip()) > 0:
                        logger.info(f"✅ Page has content ({len(body_content)} characters)")
                    else:
                        logger.warning("⚠️ Page body appears to be empty")
                except:
                    logger.warning("⚠️ Could not read page content")

            except Exception as wait_error:
                logger.warning(f"⚠️ Page load timeout: {str(wait_error)}, taking screenshot anyway")

            timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
            filename = f"3111_{timestamp}_{step_name}.png"
            local_path = os.path.join(self.local_img_path, filename)

            # Prendre un screenshot en pleine page
            logger.info("📸 Capturing screenshot...")
            page.screenshot(path=local_path, full_page=True)
            logger.info(f"✅ Screenshot saved locally: {filename}")

            # Vérifier la taille du fichier
            try:
                file_size = os.path.getsize(local_path)
                logger.info(f"📊 Screenshot file size: {file_size} bytes")
            except:
                logger.warning("⚠️ Could not check screenshot file size")

            # Upload vers Azure
            remote_path = f"{self.container_name}/screenshots/{filename}"
            upload_success = self.upload_to_azure_storage(local_path, remote_path)

            if upload_success:
                logger.info(f"✅ Screenshot uploaded: {filename}")

            return local_path

        except Exception as e:
            logger.error(f"❌ Screenshot error: {str(e)}")
            return None

    def test_db_connection(self):
        """Teste la connexion à la base de données Azure SQL"""
        db_logger = logging.getLogger('db_logger')
        db_logger.setLevel(logging.INFO)
        
        try:
            db_logger.info("========== DATABASE CONNECTION TEST ==========")
            
            # Récupérer les credentials de la DB depuis Key Vault avec le préfixe correct
            db_logger.info("Retrieving database credentials from Key Vault")
            db_server = self.get_secret("3111-db-server")
            db_logger.info(f"DB Server retrieved: {db_server}")
            
            db_name = self.get_secret("3111-db-name")
            db_logger.info(f"DB Name retrieved: {db_name}")
            
            db_username = self.get_secret("3111-db-username")
            db_logger.info(f"DB Username retrieved: {db_username}")
            
            db_password = self.get_secret("3111-db-password")
            db_logger.info("DB Password retrieved: [MASKED]")
            
            # Valeurs par défaut si les secrets ne sont pas trouvés
            if not db_server:
                db_server = "b2smepcw-sql-12345.database.windows.net"
                db_logger.info(f"Using default DB Server: {db_server}")
            
            if not db_name:
                db_name = "EON_MON_DB"
                db_logger.info(f"Using default DB Name: {db_name}")
            
            if not db_username:
                db_username = "sa1bmon"
                db_logger.info(f"Using default DB Username: {db_username}")
            
            if not db_password:
                db_password = "Affe1212"
                db_logger.info("Using default DB Password: [MASKED]")
            
            # Tester la connexion avec pymssql
            db_logger.info("Attempting to connect with pymssql...")
            conn = pymssql.connect(
                server=db_server,
                user=db_username,
                password=db_password,
                database=db_name
            )
            db_logger.info("Connection successful!")
            
            # Tester l'accès aux tables
            cursor = conn.cursor()
            db_logger.info("Testing access to tables...")
            
            # Vérifier les tables
            cursor.execute("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME IN ('Measurement', 'RESULT')")
            tables = [row[0] for row in cursor.fetchall()]
            db_logger.info(f"Found tables: {tables}")
            
            # Vérifier les colonnes
            for table in tables:
                cursor.execute(f"SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '{table}'")
                columns = cursor.fetchall()
                db_logger.info(f"Table {table} structure:")
                for col in columns:
                    db_logger.info(f"  - {col[0]}: {col[1]}")
            
            # Tester une requête simple
            db_logger.info("Testing a simple query...")
            cursor.execute("SELECT TOP 1 * FROM Measurement")
            row = cursor.fetchone()
            if row:
                db_logger.info("Query successful, sample data retrieved")
            else:
                db_logger.info("Query successful, but no data found in Measurement table")
            
            cursor.close()
            conn.close()
            db_logger.info("Database connection test completed successfully")
            return True
        
        except Exception as e:
            db_logger.error(f"Database connection test failed: {str(e)}")
            import traceback
            db_logger.error(traceback.format_exc())
            return False

    def logdb(self, login_ms_value):
        """Log les résultats dans la base de données"""
        db_logger.info("========== DATABASE OPERATION STARTED ==========")
        db_logger.info(f"Logging login time: {login_ms_value}ms")
        
        try:
            # Récupérer les informations de connexion depuis Key Vault
            db_server = self.get_secret("db-server")
            db_name = self.get_secret("db-name")
            db_username = self.get_secret("db-username")
            db_password = self.get_secret("db-password")
            
            if not all([db_server, db_name, db_username, db_password]):
                db_logger.error("❌ Failed to retrieve database credentials from Key Vault")
                return False
            
            db_logger.info("✅ Database credentials retrieved from Key Vault")
            
            # Connexion à la base de données avec pymssql
            import pymssql
            
            db_logger.info(f"Connecting to database server: {db_server}")
            conn = pymssql.connect(
                server=db_server,
                database=db_name,
                user=db_username,
                password=db_password
            )
            db_logger.info("✅ Database connection successful with pymssql")
            
            cursor = conn.cursor()
            db_logger.info("✅ Cursor created")

            MID = str(uuid.uuid4())
            TIMESTAMP = dt.datetime.now().strftime("%Y-%m-%d %H:%M:%S") + ".000"
            db_logger.info(f"Generated MID: {MID}")
            db_logger.info(f"Generated TIMESTAMP: {TIMESTAMP}")

            # Insérer dans la table Measurement
            sql_measurement = "INSERT INTO Measurement (MID,SCRIPTID,TIMESTAMP,STATIONID) VALUES (%s,%s,%s,%s)"
            db_logger.info(f"Executing SQL: {sql_measurement}")
            db_logger.info(f"With parameters: MID='{MID}', SCRIPTID='3111', TIMESTAMP='{TIMESTAMP}', STATIONID=200")
            
            cursor.execute(sql_measurement, (MID, '3111', TIMESTAMP, 200))
            db_logger.info("✅ Measurement record inserted")
            
            # Insérer dans la table RESULT
            sql_result = "INSERT INTO RESULT (MID,NAME,VALUE) VALUES (%s,%s,%s)"
            db_logger.info(f"Executing SQL: {sql_result}")
            db_logger.info(f"With parameters: MID='{MID}', NAME='LOGIN', VALUE='{login_ms_value}'")
            
            cursor.execute(sql_result, (MID, 'LOGIN', login_ms_value))
            db_logger.info("✅ Result record inserted")
            
            db_logger.info("Committing transaction...")
            conn.commit()
            db_logger.info("✅ Transaction committed")
            
            cursor.close()
            conn.close()
            db_logger.info("✅ Database connection closed")
            
            db_logger.info(f"✅ DB LOGGING COMPLETE - Login time {login_ms_value}ms recorded")
            db_logger.info("========== DATABASE OPERATION ENDED ==========")
            
            return True
        
        except Exception as e:
            db_logger.error(f"❌ Database error: {str(e)}")
            import traceback
            db_logger.error(traceback.format_exc())
            db_logger.info("========== DATABASE OPERATION FAILED ==========")
            return False

    def run(self, playwright: Playwright) -> None:
        """Fonction principale d'exécution du monitoring"""
        logger.info("=" * 50)
        logger.info("🚀 STARTING EON MONITORING (SIMPLE VERSION)")
        logger.info("=" * 50)

        try:
            # Test de connectivité Azure
            logger.info("🔐 Testing Azure connectivity...")
            test_secret = self.get_secret("eon-email")
            if not test_secret:
                logger.error("❌ Cannot retrieve test secret from Key Vault")
                sys.exit(1)

            # Récupérer les credentials depuis Key Vault
            email = self.get_secret("eon-email")
            password = self.get_secret("eon-password")

            if not email or not password:
                logger.error("❌ Unable to retrieve credentials from Key Vault")
                sys.exit(1)

            logger.info(f"✅ Credentials retrieved for email: {email[:5]}***")

            # Configuration du navigateur
            logger.info("🌐 Launching browser...")
            browser = playwright.chromium.launch(
                headless=True,  # Retour en mode headless maintenant qu'on connaît le problème
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-extensions',
                    '--disable-background-timer-throttling',
                    '--disable-renderer-backgrounding',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-blink-features=AutomationControlled',  # Éviter la détection d'automatisation
                    '--ignore-ssl-errors',  # Ignorer les erreurs SSL
                    '--ignore-certificate-errors',  # Ignorer les erreurs de certificat
                    '--ignore-ssl-errors-list',  # Ignorer la liste des erreurs SSL
                    '--ignore-certificate-errors-spki-list',  # Ignorer les erreurs SPKI
                    '--disable-web-security',  # Désactiver la sécurité web
                    '--allow-running-insecure-content',  # Permettre le contenu non sécurisé
                    '--ignore-urlfetcher-cert-requests',  # Ignorer les requêtes de certificat
                    '--disable-features=VizDisplayCompositor',  # Désactiver le compositeur d'affichage
                    '--ignore-certificate-errors-spki-list',  # Ignorer les erreurs SPKI
                    '--ignore-ssl-errors-list',  # Ignorer la liste des erreurs SSL
                    '--reduce-security-for-testing',  # Réduire la sécurité pour les tests
                    '--allow-running-insecure-content',  # Permettre le contenu non sécurisé
                    '--disable-features=TranslateUI',  # Désactiver l'interface de traduction
                    '--disable-ipc-flooding-protection',  # Désactiver la protection contre le flooding IPC
                    '--test-type',  # Mode test
                    '--disable-default-apps'  # Désactiver les applications par défaut
                ]
            )

            context = browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",  # User-Agent normal
                viewport={'width': 1920, 'height': 1080},  # Résolution plus commune
                ignore_https_errors=True,  # Ignorer les erreurs HTTPS au niveau du contexte
                extra_http_headers={
                    'Accept-Language': 'de-DE,de;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Sec-Fetch-User': '?1',
                    'Upgrade-Insecure-Requests': '1'
                }
            )
            page = context.new_page()
            page.set_default_timeout(60000)  # 60 secondes

            logger.info("✅ Browser launched successfully")

            # Test screenshot pour vérifier que le navigateur fonctionne
            try:
                page.goto("about:blank")
                self.take_screenshot(page, "BROWSER_TEST")
            except Exception as e:
                logger.warning(f"⚠️ Browser test screenshot failed: {str(e)}")

            # Variables de contrôle
            login_page_error = False
            continue_execution = True

            # Métriques de performance (initialisation avec -2000 pour indiquer "non testé")
            metrics = {
                'load_login_page': -2000,
                'login': -2000,
                'meter_reading': -2000,
                'abschlag': -2000,
                'logout': -2000
            }

            # ========== ÉTAPE 1: CHARGEMENT DE LA PAGE DE CONNEXION ==========
            logger.info("📄 Step 1: Loading login page")
            start_time = time.time()

            try:
                page.goto("https://da.eon.de/s/login/", wait_until='networkidle')

                # Gestion des popups de consentement
                try:
                    page.get_by_test_id("uc-accept-all-button").click(timeout=3000)
                    logger.info("✅ Cookie banner accepted")
                except:
                    logger.info("ℹ️ No cookie banner found")

                # Gestion de la popup de traitement des données (Datenverarbeitung)
                try:
                    # Attendre un peu pour que la popup apparaisse si elle doit apparaître
                    page.wait_for_timeout(2000)

                    # Chercher le bouton "Akzeptieren" dans la popup de consentement
                    accept_button = page.get_by_text("Akzeptieren")
                    if accept_button.is_visible(timeout=3000):
                        accept_button.click()
                        logger.info("✅ Data processing consent accepted (Akzeptieren clicked)")
                        page.wait_for_timeout(1000)  # Attendre que la popup se ferme
                    else:
                        logger.info("ℹ️ No data processing consent popup found")
                except Exception as consent_error:
                    logger.info(f"ℹ️ No data processing consent popup or error: {str(consent_error)}")

                # Remplir les champs avec des délais humains pour éviter la détection
                logger.info("📝 Filling email field...")
                email_field = page.get_by_role("textbox", name="E-Mail-Adresse")
                email_field.click()  # Cliquer d'abord pour focus
                page.wait_for_timeout(800)  # Délai humain
                email_field.fill(email)
                page.wait_for_timeout(1200)  # Délai entre les champs

                logger.info("📝 Filling password field...")
                password_field = page.get_by_role("textbox", name="Passwort")
                password_field.click()  # Cliquer d'abord pour focus
                page.wait_for_timeout(600)  # Délai humain
                password_field.fill(password)
                page.wait_for_timeout(1500)  # Délai avant soumission

                end_time = time.time()
                # Stocker directement en millisecondes
                metrics['load_login_page'] = int((end_time - start_time) * 1000)
                logger.info(f"✅ Login page loaded in {metrics['load_login_page']}ms")

            except Exception as e:
                logger.error(f"❌ Login page loading error: {str(e)}")
                metrics['load_login_page'] = -2000  # Timeout/échec de chargement de page
                continue_execution = False
                login_page_error = True
                self.take_screenshot(page, "LOAD_LOGIN_PAGE_ERROR")

            # ========== ÉTAPE 2: CONNEXION ==========
            if continue_execution:
                logger.info("🔑 Step 2: Performing login")
                start_time = time.time()

                try:
                    page.get_by_role("button", name="Anmelden").click()

                    # Wait for navigation to complete
                    page.wait_for_timeout(10000)

                    # Check the URL after login attempt
                    current_url = page.url
                    logger.info(f"📍 URL after login click: {current_url}")

                    # CHECK IF LOGIN ACTUALLY SUCCEEDED
                    if "/s/login/" in current_url:
                        logger.error("❌ LOGIN FAILED - Still on login page!")
                        logger.error("❌ Possible causes:")
                        logger.error("   - Incorrect credentials")
                        logger.error("   - CAPTCHA required")
                        logger.error("   - Account locked")
                        logger.error("   - Login form changed")

                        # Take screenshot to see what's on the login page
                        self.take_screenshot(page, "LOGIN_FAILED")

                        # Check for error messages on the page
                        try:
                            page_content = page.content()
                            if "error" in page_content.lower() or "fehler" in page_content.lower():
                                logger.error("❌ Error message detected on login page")
                            if "captcha" in page_content.lower():
                                logger.error("❌ CAPTCHA detected - manual intervention required")
                        except:
                            pass

                        # STOP EXECUTION - no point continuing
                        metrics['login'] = -2000  # Timeout/échec de connexion
                        continue_execution = False
                        end_time = time.time()
                        logger.error(f"❌ Login failed after {int((end_time - start_time) * 1000)}ms")

                    else:
                        logger.info("✅ Login appears successful - navigated away from login page")

                        # Handle different post-login scenarios
                        if "chrome-error://" in current_url or "eon.my.site.com" in current_url:
                            logger.info("🔄 Detected problematic redirect, navigating to correct URL...")
                            try:
                                page.goto("https://www.eon.de/de/mein-eon.html", wait_until='networkidle', timeout=30000)
                                logger.info("✅ Direct navigation to mein-eon.html successful")
                                current_url = page.url
                                logger.info(f"📍 New URL after direct navigation: {current_url}")
                            except Exception as direct_nav_error:
                                logger.error(f"❌ Direct navigation failed: {str(direct_nav_error)}")
                                continue_execution = False

                        elif "mein-eon.html" not in current_url and "meineonda" not in current_url:
                            logger.info("🔄 Not on expected dashboard, navigating to mein-eon.html...")
                            try:
                                page.goto("https://www.eon.de/de/mein-eon.html", wait_until='networkidle', timeout=30000)
                                logger.info("✅ Navigation to mein-eon.html successful")
                                current_url = page.url
                                logger.info(f"📍 Final URL: {current_url}")
                            except Exception as nav_error:
                                logger.error(f"❌ Navigation to dashboard failed: {str(nav_error)}")
                                continue_execution = False

                    # Only continue with post-login actions if login was successful
                    if continue_execution and "/s/login/" not in current_url:
                        try:
                            page.get_by_text("Ablehnen").click(timeout=5000)
                            logger.info("✅ Additional consent declined")
                        except:
                            logger.info("ℹ️ No additional consent dialog")

                        # Verify we're on the correct dashboard
                        final_url = page.url
                        logger.info(f"📍 Final URL after login: {final_url}")

                        if "mein-eon.html" in final_url or "meineonda" in final_url:
                            end_time = time.time()
                            # Stocker directement en millisecondes
                            metrics['login'] = int((end_time - start_time) * 1000)
                            logger.info(f"✅ Login successful in {metrics['login']}ms")

                            # Take screenshot of successful login
                            self.take_screenshot(page, "AFTER_LOGIN_SUCCESS")
                        else:
                            logger.error(f"❌ Login failed - unexpected final URL: {final_url}")
                            metrics['login'] = -2000  # Timeout/échec de navigation
                            continue_execution = False
                            self.take_screenshot(page, "LOGIN_UNEXPECTED_URL")

                except Exception as e:
                    logger.error(f"❌ Login error: {str(e)}")
                    metrics['login'] = -2000  # Timeout/erreur de connexion
                    continue_execution = False
                    self.take_screenshot(page, "LOGIN_ERROR")

            # ========== ÉTAPE 3: TEST RELEVÉ COMPTEUR ==========
            if continue_execution:
                logger.info("⚡ Step 3: Testing meter reading")
                start_time = time.time()

                try:
                    page.goto("https://www.eon.de/de/meineonda/mein-zaehlerstand.html", wait_until='networkidle')
                    page.locator("login-meter-read-submission-app").get_by_text("Zählernummer: 9701696").click(timeout=30000)

                    end_time = time.time()
                    # Stocker directement en millisecondes
                    metrics['meter_reading'] = int((end_time - start_time) * 1000)
                    logger.info(f"✅ Meter reading test successful in {metrics['meter_reading']}ms")

                except Exception as e:
                    logger.error(f"❌ Meter reading test error: {str(e)}")
                    metrics['meter_reading'] = -2000  # Timeout/échec du test de relevé

            # ========== ÉTAPE 4: TEST ACOMPTES ==========
            if continue_execution:
                logger.info("💰 Step 4: Testing installments")
                start_time = time.time()

                try:
                    page.goto("https://www.eon.de/de/meineonda/mein-abschlag.html", wait_until='networkidle')
                    page.get_by_text("Abschlagseingabe").click(timeout=30000)

                    end_time = time.time()
                    # Stocker directement en millisecondes
                    metrics['abschlag'] = int((end_time - start_time) * 1000)
                    logger.info(f"✅ Installment test successful in {metrics['abschlag']}ms")

                except Exception as e:
                    logger.error(f"❌ Installment test error: {str(e)}")
                    metrics['abschlag'] = -2000  # Timeout/échec du test d'acomptes

            # ========== ÉTAPE 5: DÉCONNEXION ==========
            logger.info("🚪 Step 5: Logging out")
            start_time = time.time()

            try:
                # Plusieurs méthodes pour trouver le bouton de déconnexion
                logout_found = False
                
                # Méthode 1: Utiliser le sélecteur CSS spécifique
                try:
                    logger.info("Trying logout method 1: CSS selector")
                    page.locator("eon-ui-website-navigation-main-link[internal-title='Logout']").click(timeout=5000)
                    logout_found = True
                    logger.info("✅ Logout button found with CSS selector")
                except Exception as e:
                    logger.info(f"Method 1 failed: {str(e)}")
                
                # Méthode 2: Utiliser l'icône
                if not logout_found:
                    try:
                        logger.info("Trying logout method 2: Icon name")
                        page.locator("eon-ui-icon[name='logout']").click(timeout=5000)
                        logout_found = True
                        logger.info("✅ Logout button found with icon name")
                    except Exception as e:
                        logger.info(f"Method 2 failed: {str(e)}")
                
                # Méthode 3: Utiliser le titre
                if not logout_found:
                    try:
                        logger.info("Trying logout method 3: Title attribute")
                        page.locator("a[title='Logout']").click(timeout=5000)
                        logout_found = True
                        logger.info("✅ Logout button found with title attribute")
                    except Exception as e:
                        logger.info(f"Method 3 failed: {str(e)}")
                
                # Méthode 4: Ancienne méthode (fallback)
                if not logout_found:
                    try:
                        logger.info("Trying logout method 4: Original method")
                        page.get_by_role("menuitem", name="Navigiere zu https://www.eon.de/de/pk.html").locator("svg").click(timeout=5000)
                        logout_found = True
                        logger.info("✅ Logout button found with original method")
                    except Exception as e:
                        logger.info(f"Method 4 failed: {str(e)}")
                
                # Vérifier si on a réussi à trouver et cliquer sur le bouton de déconnexion
                if not logout_found:
                    raise Exception("Could not find logout button with any method")
                
                # Attendre la page de confirmation de déconnexion
                logger.info("Waiting for logout confirmation page...")
                
                # Plusieurs méthodes pour vérifier la déconnexion
                logout_confirmed = False
                
                # Méthode 1: Attendre l'URL de déconnexion
                try:
                    page.wait_for_url("https://www.eon.de/de/meineon/logout.html", timeout=10000)
                    logout_confirmed = True
                    logger.info("✅ Logout confirmed by URL")
                except Exception as e:
                    logger.info(f"Logout URL wait failed: {str(e)}")
                
                # Méthode 2: Chercher le message de confirmation
                if not logout_confirmed:
                    try:
                        page.wait_for_selector("text=Sie haben sich erfolgreich abgemeldet", timeout=10000)
                        logout_confirmed = True
                        logger.info("✅ Logout confirmed by success message")
                    except Exception as e:
                        logger.info(f"Logout message wait failed: {str(e)}")
                
                # Si la déconnexion est confirmée, calculer le temps
                if logout_confirmed:
                    end_time = time.time()
                    # Stocker directement en millisecondes
                    metrics['logout'] = int((end_time - start_time) * 1000)
                    logger.info(f"✅ Logout successful in {metrics['logout']}ms")
                    
                    # Prendre une capture d'écran de la page de déconnexion
                    self.take_screenshot(page, "LOGOUT_SUCCESS")
                else:
                    logger.error("❌ Could not confirm successful logout")
                    metrics['logout'] = -2000  # Échec de déconnexion
                    self.take_screenshot(page, "LOGOUT_FAILURE")

            except Exception as e:
                logger.error(f"❌ Logout error: {str(e)}")
                metrics['logout'] = -2000  # Timeout/échec de déconnexion
                self.take_screenshot(page, "LOGOUT_ERROR")

            # Fermeture du navigateur
            context.close()
            browser.close()
            logger.info("✅ Browser closed")

            # ========== ENREGISTREMENT DES RÉSULTATS ==========
            if not login_page_error:
                logger.info("💾 Saving results...")
                # Passer directement les métriques en secondes à log_results
                # La conversion en ms sera faite UNE SEULE FOIS dans log_results
                self.log_results(metrics)
                logger.info("✅ Results saved successfully")
            else:
                logger.error("❌ Execution failed - no results recorded")

        except Exception as e:
            logger.error(f"💥 Critical error during monitoring: {str(e)}")
            sys.exit(1)

        finally:
            logger.info("🏁 EON MONITORING COMPLETED")
            logger.info("=" * 50)

def main():
    """Point d'entrée principal"""
    try:
        logger.info("🎬 Starting EON Monitoring Application (Simple Version)")
        monitoring = SimpleEonMonitoring()

        with sync_playwright() as playwright:
            monitoring.run(playwright)

        logger.info("🎉 EON Monitoring Application completed successfully")

    except Exception as e:
        logger.error(f"💥 Application failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
