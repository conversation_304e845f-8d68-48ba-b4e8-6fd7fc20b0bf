#!/bin/bash

# SAP Playwright Container Monitor Script - FIXED VERSION
# Monitor execution and retrieve logs from SAP Playwright testing containers
# Project 10001

set -e

# Configuration
RESOURCE_GROUP="RG-EDGMON-RUN"

echo "🔍 SAP Playwright Container Monitor - Project 10001"
echo "="*80

# Function to list all sap-playwright containers
list_containers() {
    echo "📋 Listing all sap-playwright-tester containers..."
    az container list \
        --resource-group $RESOURCE_GROUP \
        --query "[?contains(name, 'sap-playwright-tester')].{Name:name, Status:provisioningState, State:containers[0].instanceView.currentState.state, RestartPolicy:restartPolicy, CreatedTime:creationTime}" \
        --output table
}

# Function to get detailed status of a specific container
get_container_status() {
    local container_name=$1
    echo "🔍 Detailed status for container: $container_name"
    echo "-"*50
    
    az container show \
        --resource-group $RESOURCE_GROUP \
        --name $container_name \
        --query "{Name:name, Status:provisioningState, State:containers[0].instanceView.currentState.state, StartTime:containers[0].instanceView.currentState.startTime, FinishTime:containers[0].instanceView.currentState.finishTime, ExitCode:containers[0].instanceView.currentState.exitCode, RestartCount:containers[0].instanceView.restartCount, CPU:containers[0].resources.requests.cpu, Memory:containers[0].resources.requests.memoryInGB}" \
        --output table
}

# Function to get container logs with better formatting
get_container_logs() {
    local container_name=$1
    echo "📋 Logs for container: $container_name"
    echo "-"*50
    
    # Get logs and add timestamps if available
    logs=$(az container logs \
        --resource-group $RESOURCE_GROUP \
        --name $container_name)
    
    if [ -z "$logs" ]; then
        echo "⚠️  No logs available yet. Container might still be starting..."
    else
        echo "$logs"
    fi
}

# Function to monitor container in real-time
monitor_container() {
    local container_name=$1
    echo "📊 Real-time monitoring for container: $container_name"
    echo "Press Ctrl+C to stop monitoring"
    echo "-"*50
    
    local last_log_check=""
    
    while true; do
        echo "🕒 $(date '+%Y-%m-%d %H:%M:%S')"
        
        # Get current state
        local state=$(az container show \
            --resource-group $RESOURCE_GROUP \
            --name $container_name \
            --query "containers[0].instanceView.currentState.state" \
            --output tsv 2>/dev/null || echo "Not Found")
        
        local exit_code=$(az container show \
            --resource-group $RESOURCE_GROUP \
            --name $container_name \
            --query "containers[0].instanceView.currentState.exitCode" \
            --output tsv 2>/dev/null || echo "N/A")
        
        echo "   State: $state (Exit Code: $exit_code)"
        
        # Show recent logs
        local current_logs=$(az container logs \
            --resource-group $RESOURCE_GROUP \
            --name $container_name 2>/dev/null || echo "")
        
        if [ "$current_logs" != "$last_log_check" ] && [ -n "$current_logs" ]; then
            echo "   📄 Latest logs:"
            echo "$current_logs" | tail -3 | sed 's/^/      /'
            last_log_check="$current_logs"
        fi
        
        if [ "$state" = "Terminated" ] || [ "$state" = "Not Found" ]; then
            if [ "$exit_code" = "0" ]; then
                echo "✅ Container completed successfully!"
            else
                echo "❌ Container finished with errors (Exit Code: $exit_code)"
            fi
            break
        fi
        
        sleep 15
    done
}

# FIXED: Function to find the latest sap-playwright-tester container
find_latest_container() {
    # Try different approaches to find containers (silent version)
    local container_name
    
    # Method 1: Try with creation time sorting
    container_name=$(az container list \
        --resource-group $RESOURCE_GROUP \
        --query "[?contains(name, 'sap-playwright-tester') && creationTime != null] | sort_by(@, &creationTime) | [-1].name" \
        --output tsv 2>/dev/null || echo "")
    
    if [ -n "$container_name" ] && [ "$container_name" != "null" ]; then
        echo $container_name
        return
    fi
    
    # Method 2: Get all containers and sort by name (contains timestamp)
    container_name=$(az container list \
        --resource-group $RESOURCE_GROUP \
        --query "[?contains(name, 'sap-playwright-tester')].name" \
        --output tsv 2>/dev/null | sort -r | head -1)
    
    if [ -n "$container_name" ] && [ "$container_name" != "null" ]; then
        echo $container_name
        return
    fi
    
    # Method 3: Manual search through all containers
    local all_containers=$(az container list \
        --resource-group $RESOURCE_GROUP \
        --query "[].name" \
        --output tsv 2>/dev/null || echo "")
    
    for container in $all_containers; do
        if [[ $container == *"sap-playwright-tester"* ]]; then
            echo $container
            return
        fi
    done
    
    echo ""
}

# Verbose version for debugging
find_latest_container_verbose() {
    echo "🔍 Searching for latest sap-playwright-tester container..."
    
    # Try different approaches to find containers
    local container_name
    
    # Method 1: Try with creation time sorting
    container_name=$(az container list \
        --resource-group $RESOURCE_GROUP \
        --query "[?contains(name, 'sap-playwright-tester') && creationTime != null] | sort_by(@, &creationTime) | [-1].name" \
        --output tsv 2>/dev/null || echo "")
    
    if [ -n "$container_name" ] && [ "$container_name" != "null" ]; then
        echo "   Found using creation time: $container_name"
        echo $container_name
        return
    fi
    
    # Method 2: Get all containers and sort by name (contains timestamp)
    container_name=$(az container list \
        --resource-group $RESOURCE_GROUP \
        --query "[?contains(name, 'sap-playwright-tester')].name" \
        --output tsv 2>/dev/null | sort -r | head -1)
    
    if [ -n "$container_name" ] && [ "$container_name" != "null" ]; then
        echo "   Found using name sorting: $container_name"
        echo $container_name
        return
    fi
    
    # Method 3: Manual search through all containers
    echo "   Performing manual search..."
    local all_containers=$(az container list \
        --resource-group $RESOURCE_GROUP \
        --query "[].name" \
        --output tsv 2>/dev/null || echo "")
    
    for container in $all_containers; do
        if [[ $container == *"sap-playwright-tester"* ]]; then
            echo "   Found manually: $container"
            echo $container
            return
        fi
    done
    
    echo "   No containers found"
    echo ""
}

# Function to get comprehensive test results summary
get_test_summary() {
    local container_name=$1
    echo "📊 SAP Playwright Test Summary for: $container_name"
    echo "="*80
    
    # Get container status
    local state=$(az container show \
        --resource-group $RESOURCE_GROUP \
        --name $container_name \
        --query "containers[0].instanceView.currentState.state" \
        --output tsv 2>/dev/null || echo "Not Found")
    
    local exit_code=$(az container show \
        --resource-group $RESOURCE_GROUP \
        --name $container_name \
        --query "containers[0].instanceView.currentState.exitCode" \
        --output tsv 2>/dev/null || echo "N/A")
        
    local start_time=$(az container show \
        --resource-group $RESOURCE_GROUP \
        --name $container_name \
        --query "containers[0].instanceView.currentState.startTime" \
        --output tsv 2>/dev/null || echo "N/A")
        
    local finish_time=$(az container show \
        --resource-group $RESOURCE_GROUP \
        --name $container_name \
        --query "containers[0].instanceView.currentState.finishTime" \
        --output tsv 2>/dev/null || echo "N/A")
    
    echo "🔍 Container Information:"
    echo "   • State: $state"
    echo "   • Exit Code: $exit_code"
    echo "   • Started: $start_time"
    echo "   • Finished: $finish_time"
    echo ""
    
    # Interpret results
    if [ "$exit_code" = "0" ]; then
        echo "✅ SAP Playwright Test: SUCCESS"
        echo "🎯 The SAP Launchpad was accessible and loaded successfully"
        echo "📸 Screenshot should be available"
        echo "📄 Detailed JSON results should be generated"
    elif [ "$exit_code" != "N/A" ] && [ "$exit_code" != "" ] && [ "$exit_code" != "null" ]; then
        echo "❌ SAP Playwright Test: FAILED (Exit Code: $exit_code)"
        echo "⚠️  The test encountered issues accessing the SAP Launchpad"
        echo "🔍 Check the logs below for detailed error information"
    else
        echo "⏳ SAP Playwright Test: IN PROGRESS OR UNKNOWN"
        if [ "$state" = "Running" ]; then
            echo "🔄 Container is currently executing the test"
        fi
    fi
    
    echo ""
    echo "📋 Complete execution logs:"
    echo "-"*50
    get_container_logs "$container_name"
    echo ""
    echo "="*80
}

# Main menu
show_menu() {
    echo ""
    echo "🎯 SAP Playwright Container Monitor Options:"
    echo "1. List all SAP Playwright containers"
    echo "2. Monitor latest container (real-time)"
    echo "3. Get status of latest container"
    echo "4. Get logs of latest container"
    echo "5. Get complete test summary of latest container"
    echo "6. Get status of specific container"
    echo "7. Get logs of specific container"
    echo "8. Get test summary of specific container"
    echo "9. Find latest container name only"
    echo "0. Exit"
    echo ""
    read -p "Choose an option (0-9): " choice
}

# Command-line argument handling
case "${1:-menu}" in
    "list")
        list_containers
        ;;
    "latest-status")
        LATEST_CONTAINER=$(find_latest_container)
        if [ -n "$LATEST_CONTAINER" ]; then
            get_container_status "$LATEST_CONTAINER"
        else
            echo "❌ No sap-playwright-tester containers found"
        fi
        ;;
    "latest-logs")
        LATEST_CONTAINER=$(find_latest_container)
        if [ -n "$LATEST_CONTAINER" ]; then
            get_container_logs "$LATEST_CONTAINER"
        else
            echo "❌ No sap-playwright-tester containers found"
        fi
        ;;
    "latest-summary")
        LATEST_CONTAINER=$(find_latest_container)
        if [ -n "$LATEST_CONTAINER" ]; then
            get_test_summary "$LATEST_CONTAINER"
        else
            echo "❌ No sap-playwright-tester containers found"
        fi
        ;;
    "monitor")
        LATEST_CONTAINER=$(find_latest_container)
        if [ -n "$LATEST_CONTAINER" ]; then
            monitor_container "$LATEST_CONTAINER"
        else
            echo "❌ No sap-playwright-tester containers found"
        fi
        ;;
    "find-latest")
        LATEST_CONTAINER=$(find_latest_container_verbose)
        if [ -n "$LATEST_CONTAINER" ]; then
            echo "Latest container: $LATEST_CONTAINER"
        else
            echo "❌ No sap-playwright-tester containers found"
        fi
        ;;
    "menu"|*)
        while true; do
            show_menu
            case $choice in
                1)
                    list_containers
                    ;;
                2)
                    LATEST_CONTAINER=$(find_latest_container)
                    if [ -n "$LATEST_CONTAINER" ]; then
                        monitor_container "$LATEST_CONTAINER"
                    else
                        echo "❌ No sap-playwright-tester containers found"
                    fi
                    ;;
                3)
                    LATEST_CONTAINER=$(find_latest_container)
                    if [ -n "$LATEST_CONTAINER" ]; then
                        get_container_status "$LATEST_CONTAINER"
                    else
                        echo "❌ No sap-playwright-tester containers found"
                    fi
                    ;;
                4)
                    LATEST_CONTAINER=$(find_latest_container)
                    if [ -n "$LATEST_CONTAINER" ]; then
                        get_container_logs "$LATEST_CONTAINER"
                    else
                        echo "❌ No sap-playwright-tester containers found"
                    fi
                    ;;
                5)
                    LATEST_CONTAINER=$(find_latest_container)
                    if [ -n "$LATEST_CONTAINER" ]; then
                        get_test_summary "$LATEST_CONTAINER"
                    else
                        echo "❌ No sap-playwright-tester containers found"
                    fi
                    ;;
                6)
                    list_containers
                    echo ""
                    read -p "Enter container name: " container_name
                    if [ -n "$container_name" ]; then
                        get_container_status "$container_name"
                    fi
                    ;;
                7)
                    list_containers
                    echo ""
                    read -p "Enter container name: " container_name
                    if [ -n "$container_name" ]; then
                        get_container_logs "$container_name"
                    fi
                    ;;
                8)
                    list_containers
                    echo ""
                    read -p "Enter container name: " container_name
                    if [ -n "$container_name" ]; then
                        get_test_summary "$container_name"
                    fi
                    ;;
                9)
                    find_latest_container_verbose
                    ;;
                0)
                    echo "👋 Goodbye!"
                    exit 0
                    ;;
                *)
                    echo "❌ Invalid option. Please choose 0-9."
                    ;;
            esac
            echo ""
            read -p "Press Enter to continue..."
        done
        ;;
esac 