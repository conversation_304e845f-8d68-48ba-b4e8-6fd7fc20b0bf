#!/usr/bin/env python3

"""
SAP Playwright Enterprise Connectivity Tester
Production-ready version with Azure KeyVault, Blob Storage, and Splunk integration
Based on 8810 architecture patterns
"""

import asyncio
import json
import os
import traceback
import logging
from datetime import datetime, timezone, date
from playwright.async_api import async_playwright
from azure.identity import DefaultAzureCredential
from azure.keyvault.secrets import SecretClient
from azure.storage.blob import BlobServiceClient, ContentSettings

# ==============================================
# ENTERPRISE CONFIGURATION
# ==============================================

# Script Identification
SCRIPT_ID = "10001"
SCRIPT_NAME = "SAP_PLAYWRIGHT_ENTERPRISE"

# Azure Configuration from Environment Variables
KEY_VAULT_NAME = os.environ.get("KEY_VAULT_NAME")
STORAGE_ACCOUNT_NAME = os.environ.get("STORAGE_ACCOUNT_NAME")
STORAGE_CONTAINER_NAME = os.environ.get("STORAGE_CONTAINER_NAME", "monitoring-results")
BLOB_PREFIX = os.environ.get("BLOB_PREFIX", "10001_SAP_PLAYWRIGHT")

# SAP Configuration
SAP_KEYVAULT_SECRET = os.environ.get("SAP_KEYVAULT_SECRET", "SAP-LAUNCHPAD-CONFIG")
TARGET_URL = os.getenv("TARGET_URL", "https://oneb-central-dev.launchpad.cfapps.eu10.hana.ondemand.com/site?siteId=1af3caee-8117-49e3-b787-7ad74914b5cb")

# Test Configuration
PAGE_TIMEOUT = int(os.getenv("PAGE_TIMEOUT", "60000"))
NAVIGATION_TIMEOUT = int(os.getenv("NAVIGATION_TIMEOUT", "30000"))
BROWSER_TYPE = os.getenv("BROWSER_TYPE", "chromium")
HEADLESS = os.getenv("HEADLESS", "true").lower() in ("true", "1", "yes")
VIEWPORT_WIDTH = int(os.getenv("VIEWPORT_WIDTH", "1920"))
VIEWPORT_HEIGHT = int(os.getenv("VIEWPORT_HEIGHT", "1080"))

# Output Configuration
TAKE_SCREENSHOT = os.getenv("TAKE_SCREENSHOT", "true").lower() in ("true", "1", "yes")
LOCAL_RESULTS_DIR = "/app/results"
LOCAL_SCREENSHOTS_DIR = "/app/screenshots"

# Splunk Configuration
SPLUNK_ENABLED = os.getenv("SPLUNK_ENABLED", "false").lower() in ("true", "1", "yes")
SPLUNK_TOKEN = os.getenv("SPLUNK_TOKEN")
SPLUNK_URL = os.getenv("SPLUNK_URL")

# ==============================================
# AZURE INTEGRATION FUNCTIONS
# ==============================================

def get_sap_config_from_keyvault():
    """Retrieve SAP configuration from Azure Key Vault"""
    try:
        print("🔐 Retrieving SAP configuration from Azure Key Vault...")
        
        if not KEY_VAULT_NAME:
            print("⚠️  KEY_VAULT_NAME not configured, using environment variables")
            return {
                "url": TARGET_URL,
                "username": os.getenv("SAP_USERNAME", ""),
                "password": os.getenv("SAP_PASSWORD", ""),
                "expected_keywords": os.getenv("EXPECTED_KEYWORDS", "launchpad,sap,fiori").split(",")
            }
        
        credential = DefaultAzureCredential()
        vault_url = f"https://{KEY_VAULT_NAME}.vault.azure.net"
        client = SecretClient(vault_url=vault_url, credential=credential)
        
        secret = client.get_secret(SAP_KEYVAULT_SECRET)
        config = json.loads(secret.value)
        
        print("✅ SAP configuration retrieved from Key Vault")
        return config
        
    except Exception as e:
        print(f"❌ Error retrieving SAP config from Key Vault: {e}")
        print("🔄 Falling back to environment variables...")
        return {
            "url": TARGET_URL,
            "username": os.getenv("SAP_USERNAME", ""),
            "password": os.getenv("SAP_PASSWORD", ""),
            "expected_keywords": os.getenv("EXPECTED_KEYWORDS", "launchpad,sap,fiori").split(",")
        }

def upload_to_blob_storage(file_path, blob_name, content_type="application/octet-stream"):
    """Upload file to Azure Blob Storage"""
    try:
        if not STORAGE_ACCOUNT_NAME:
            print("⚠️  STORAGE_ACCOUNT_NAME not configured, keeping files local")
            return None
            
        print(f"📤 Uploading {blob_name} to Azure Blob Storage...")
        
        credential = DefaultAzureCredential()
        blob_service_client = BlobServiceClient(
            account_url=f"https://{STORAGE_ACCOUNT_NAME}.blob.core.windows.net",
            credential=credential
        )
        
        container_client = blob_service_client.get_container_client(STORAGE_CONTAINER_NAME)
        
        # Create container if it doesn't exist
        if not container_client.exists():
            container_client.create_container()
        
        # Upload file
        with open(file_path, "rb") as data:
            blob_client = container_client.upload_blob(
                name=blob_name,
                data=data,
                content_settings=ContentSettings(content_type=content_type),
                overwrite=True
            )
        
        blob_url = f"https://{STORAGE_ACCOUNT_NAME}.blob.core.windows.net/{STORAGE_CONTAINER_NAME}/{blob_name}"
        print(f"✅ Uploaded to: {blob_url}")
        return blob_url
        
    except Exception as e:
        print(f"❌ Error uploading to Blob Storage: {e}")
        return None

def log_to_structured_csv(test_results):
    """Log results in structured CSV format for enterprise monitoring"""
    try:
        today = date.today().strftime("%Y%m%d")
        timestamp = datetime.now().strftime("%H:%M:%S")
        date_str = date.today().strftime("%d.%m.%Y")
        
        # Structured log line compatible with 8810 format
        status_ok = "OK" if test_results.get("success", False) else "NOK"
        load_time = f"{test_results.get('performance', {}).get('navigation_time', 0):.2f}s"
        http_status = test_results.get("connectivity", {}).get("status_code", "N/A")
        js_errors = len(test_results.get("page_info", {}).get("javascript_errors", []))
        screenshot_status = "OK" if test_results.get("screenshot_taken", False) else "NOK"
        
        log_line = f"{SCRIPT_ID};{date_str};{timestamp};{status_ok};{load_time};{http_status};{js_errors};{screenshot_status};"
        
        # Local CSV logging
        os.makedirs(LOCAL_RESULTS_DIR, exist_ok=True)
        local_csv = f"{LOCAL_RESULTS_DIR}/{BLOB_PREFIX}_{today}.csv"
        
        if not os.path.exists(local_csv):
            with open(local_csv, 'w') as f:
                f.write("SCRIPT_ID;SCRIPT_DATE;SCRIPT_TIME;TEST_STATUS;LOAD_TIME;HTTP_STATUS;JS_ERRORS;SCREENSHOT_STATUS;\n")
        
        with open(local_csv, 'a') as f:
            f.write(log_line + "\n")
        
        print(f"📊 Logged to CSV: {local_csv}")
        
        # Upload to Blob Storage
        if STORAGE_ACCOUNT_NAME:
            blob_name = f"logs/{BLOB_PREFIX}_{today}.csv"
            upload_to_blob_storage(local_csv, blob_name, "text/csv")
        
        return log_line
        
    except Exception as e:
        print(f"❌ Error logging to CSV: {e}")
        return None

def send_to_splunk(test_results):
    """Send results to Splunk for enterprise monitoring"""
    try:
        if not SPLUNK_ENABLED or not SPLUNK_TOKEN or not SPLUNK_URL:
            print("⚠️  Splunk not configured, skipping...")
            return
        
        print("📊 Sending results to Splunk...")
        
        # Prepare Splunk event
        splunk_event = {
            "time": int(datetime.now().timestamp()),
            "source": "sap_playwright_monitor",
            "sourcetype": "sap_monitoring",
            "index": "monitoring",
            "event": {
                "script_id": SCRIPT_ID,
                "script_name": SCRIPT_NAME,
                "test_url": TARGET_URL,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                **test_results
            }
        }
        
        # Note: Actual Splunk HTTP Event Collector implementation would go here
        # For now, log the structure
        splunk_file = f"{LOCAL_RESULTS_DIR}/splunk_events.jsonl"
        with open(splunk_file, 'a') as f:
            f.write(json.dumps(splunk_event) + "\n")
        
        print("✅ Splunk event logged locally (configure SPLUNK_URL for real sending)")
        
    except Exception as e:
        print(f"❌ Error sending to Splunk: {e}")

# ==============================================
# ENTERPRISE SAP PLAYWRIGHT TESTER
# ==============================================

class EnterpriseSAPPlaywrightTester:
    def __init__(self):
        self.sap_config = get_sap_config_from_keyvault()
        self.results = {
            "test_info": {
                "script_id": SCRIPT_ID,
                "script_name": SCRIPT_NAME,
                "url": self.sap_config.get("url", TARGET_URL),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "browser": BROWSER_TYPE,
                "environment": {
                    "keyvault_enabled": bool(KEY_VAULT_NAME),
                    "storage_enabled": bool(STORAGE_ACCOUNT_NAME),
                    "splunk_enabled": SPLUNK_ENABLED
                }
            },
            "connectivity": {},
            "page_info": {},
            "performance": {},
            "enterprise": {
                "blob_urls": {},
                "csv_logged": False,
                "splunk_sent": False
            },
            "errors": [],
            "success": False,
            "progress": []
        }
    
    def log_progress(self, step, message, success=True):
        """Enterprise progress logging"""
        timestamp = datetime.now(timezone.utc).isoformat()
        status = "✅" if success else "❌"
        progress_msg = f"{status} [{SCRIPT_ID}] {step}: {message}"
        print(progress_msg)
        
        self.results["progress"].append({
            "step": step,
            "message": message,
            "success": success,
            "timestamp": timestamp
        })
    
    async def run_enterprise_test(self):
        """Main enterprise test execution"""
        print("="*100)
        print(f"🏢 SAP PLAYWRIGHT ENTERPRISE TESTER - {SCRIPT_NAME}")
        print("="*100)
        print(f"🎯 Target URL: {self.sap_config.get('url', TARGET_URL)}")
        print(f"🕒 Test started: {datetime.now().isoformat()}")
        print(f"🔐 Key Vault: {'✅' if KEY_VAULT_NAME else '❌'}")
        print(f"📦 Blob Storage: {'✅' if STORAGE_ACCOUNT_NAME else '❌'}")
        print(f"📊 Splunk: {'✅' if SPLUNK_ENABLED else '❌'}")
        print("-" * 100)
        
        start_time = datetime.now()
        
        try:
            self.log_progress("1/7", "Initializing Playwright environment")
            async with async_playwright() as p:
                
                # Launch browser
                self.log_progress("2/7", f"Launching {BROWSER_TYPE} browser")
                browser = await self._launch_browser(p)
                
                # Create and configure page
                self.log_progress("3/7", "Creating and configuring page")
                page = await browser.new_page()
                await self._configure_page(page)
                
                # Navigate to SAP URL
                self.log_progress("4/7", "Navigating to SAP Launchpad")
                response = await self._navigate_to_sap(page)
                
                # Analyze page content
                self.log_progress("5/7", "Analyzing page content and performance")
                await self._analyze_sap_page(page, response)
                
                # Take enterprise screenshot
                if TAKE_SCREENSHOT:
                    self.log_progress("6/7", "Taking enterprise screenshot")
                    await self._take_enterprise_screenshot(page)
                
                # Enterprise logging and storage
                self.log_progress("7/7", "Uploading results to enterprise systems")
                await self._handle_enterprise_storage()
                
                await browser.close()
                
                total_time = (datetime.now() - start_time).total_seconds()
                self.results["performance"]["total_test_time"] = total_time
                self.results["success"] = True
                
                self.log_progress("COMPLETE", f"Enterprise test completed in {total_time:.2f}s")
                
        except Exception as e:
            error_msg = f"Enterprise test failed: {str(e)}"
            traceback_str = traceback.format_exc()
            print(f"❌ {error_msg}")
            print(f"📋 Traceback:\n{traceback_str}")
            
            self.log_progress("ERROR", error_msg, success=False)
            
            self.results["errors"].append({
                "type": "enterprise_test_error",
                "message": error_msg,
                "traceback": traceback_str,
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
            self.results["success"] = False
    
    async def _launch_browser(self, playwright):
        """Launch browser for enterprise testing"""
        browser_options = {"headless": HEADLESS}
        
        if BROWSER_TYPE == "chromium":
            browser = await playwright.chromium.launch(**browser_options)
        elif BROWSER_TYPE == "firefox":
            browser = await playwright.firefox.launch(**browser_options)
        elif BROWSER_TYPE == "webkit":
            browser = await playwright.webkit.launch(**browser_options)
        else:
            raise ValueError(f"Unsupported browser: {BROWSER_TYPE}")
        
        return browser
    
    async def _configure_page(self, page):
        """Configure page for enterprise testing"""
        await page.set_viewport_size(width=VIEWPORT_WIDTH, height=VIEWPORT_HEIGHT)
        page.set_default_timeout(PAGE_TIMEOUT)
        page.set_default_navigation_timeout(NAVIGATION_TIMEOUT)
    
    async def _navigate_to_sap(self, page):
        """Navigate to SAP Launchpad"""
        url = self.sap_config.get("url", TARGET_URL)
        print(f"   🔗 Navigating to: {url}")
        
        response = await page.goto(url, wait_until="domcontentloaded")
        
        self.results["connectivity"] = {
            "status_code": response.status,
            "status_text": response.status_text,
            "url": response.url,
            "ok": response.ok,
            "redirected": response.url != url
        }
        
        print(f"   📡 HTTP {response.status} {response.status_text}")
        return response
    
    async def _analyze_sap_page(self, page, response):
        """Enterprise SAP page analysis"""
        try:
            # Get page information
            title = await page.title()
            url = page.url
            
            # JavaScript error monitoring
            js_errors = []
            page.on("pageerror", lambda error: js_errors.append(str(error)))
            
            # Wait for page to load
            await asyncio.sleep(3)
            
            # Check for expected SAP elements
            expected_keywords = self.sap_config.get("expected_keywords", ["launchpad", "sap", "fiori"])
            matched_keywords = [kw for kw in expected_keywords if kw.lower() in title.lower()]
            
            # Get performance metrics
            try:
                metrics = await page.evaluate("() => JSON.stringify(performance.getEntriesByType('navigation')[0])")
                performance_data = json.loads(metrics) if metrics else {}
            except:
                performance_data = {}
            
            # Get content length
            try:
                content_length = await page.evaluate("() => document.documentElement.outerHTML.length")
            except:
                content_length = 0
            
            self.results["page_info"] = {
                "title": title,
                "final_url": url,
                "title_matched_keywords": matched_keywords,
                "content_length": content_length,
                "javascript_errors": js_errors,
                "javascript_error_count": len(js_errors),
                "performance_metrics": performance_data,
                "sap_elements_detected": len(matched_keywords) > 0
            }
            
            print(f"   📄 Page: {title}")
            print(f"   🎯 Keywords matched: {matched_keywords}")
            print(f"   ⚠️  JS errors: {len(js_errors)}")
            
        except Exception as e:
            print(f"   ❌ Page analysis failed: {e}")
            raise
    
    async def _take_enterprise_screenshot(self, page):
        """Take and upload enterprise screenshot"""
        try:
            os.makedirs(LOCAL_SCREENSHOTS_DIR, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_filename = f"{SCRIPT_ID}_screenshot_{timestamp}.png"
            local_path = f"{LOCAL_SCREENSHOTS_DIR}/{screenshot_filename}"
            
            print(f"   📸 Taking screenshot: {screenshot_filename}")
            await page.screenshot(path=local_path, full_page=True)
            
            # Upload to Blob Storage
            blob_name = f"screenshots/{screenshot_filename}"
            blob_url = upload_to_blob_storage(local_path, blob_name, "image/png")
            
            self.results["screenshot_taken"] = True
            self.results["screenshot_local_path"] = local_path
            if blob_url:
                self.results["enterprise"]["blob_urls"]["screenshot"] = blob_url
            
            print(f"   ✅ Screenshot saved locally and uploaded to cloud")
            
        except Exception as e:
            print(f"   ❌ Screenshot failed: {e}")
            self.results["errors"].append({
                "type": "screenshot_error",
                "message": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
    
    async def _handle_enterprise_storage(self):
        """Handle enterprise storage and logging"""
        try:
            # Save JSON results locally
            os.makedirs(LOCAL_RESULTS_DIR, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            json_filename = f"{SCRIPT_ID}_results_{timestamp}.json"
            local_json_path = f"{LOCAL_RESULTS_DIR}/{json_filename}"
            
            with open(local_json_path, 'w') as f:
                json.dump(self.results, f, indent=2, default=str)
            
            # Upload JSON to Blob Storage
            blob_name = f"results/{json_filename}"
            json_blob_url = upload_to_blob_storage(local_json_path, blob_name, "application/json")
            if json_blob_url:
                self.results["enterprise"]["blob_urls"]["results"] = json_blob_url
            
            # Structured CSV logging
            csv_line = log_to_structured_csv(self.results)
            self.results["enterprise"]["csv_logged"] = bool(csv_line)
            
            # Send to Splunk
            send_to_splunk(self.results)
            self.results["enterprise"]["splunk_sent"] = SPLUNK_ENABLED
            
        except Exception as e:
            print(f"❌ Enterprise storage failed: {e}")
            raise
    
    def print_enterprise_summary(self):
        """Print enterprise test summary"""
        print("\n" + "="*100)
        print(f"📊 ENTERPRISE TEST SUMMARY - {SCRIPT_NAME}")
        print("="*100)
        
        success = self.results["success"]
        status_emoji = "✅" if success else "❌"
        print(f"{status_emoji} Test Status: {'SUCCESS' if success else 'FAILED'}")
        
        if "connectivity" in self.results:
            conn = self.results["connectivity"]
            print(f"🌐 HTTP Response: {conn.get('status_code')} {conn.get('status_text')}")
        
        if "page_info" in self.results:
            page = self.results["page_info"]
            print(f"📄 SAP Elements: {'✅' if page.get('sap_elements_detected') else '❌'}")
            print(f"⚠️  JS Errors: {page.get('javascript_error_count', 0)}")
        
        # Enterprise features
        enterprise = self.results.get("enterprise", {})
        print(f"📦 Blob Storage: {'✅' if enterprise.get('blob_urls') else '❌'}")
        print(f"📊 CSV Logged: {'✅' if enterprise.get('csv_logged') else '❌'}")
        print(f"📊 Splunk: {'✅' if enterprise.get('splunk_sent') else '❌'}")
        
        error_count = len(self.results.get("errors", []))
        if error_count > 0:
            print(f"⚠️  Errors: {error_count}")
        
        print("="*100)

async def main():
    """Enterprise main execution"""
    tester = EnterpriseSAPPlaywrightTester()
    
    try:
        await tester.run_enterprise_test()
    except Exception as e:
        print(f"💥 Unexpected enterprise error: {e}")
        tester.results["success"] = False
    
    tester.print_enterprise_summary()
    
    # Exit with enterprise status code
    exit_code = 0 if tester.results["success"] else 1
    print(f"🏁 Enterprise test exiting with code: {exit_code}")
    exit(exit_code)

if __name__ == "__main__":
    asyncio.run(main()) 