#!/usr/bin/env python3
import time
import requests
import json
import os
import socket
import subprocess
import platform
import psutil
import ssl
from datetime import datetime
from urllib.parse import urlparse

def get_container_network_info():
    """Gets detailed network information from the container"""
    print("🔍 Collecting container network information...")
    
    network_info = {
        "container_info": {},
        "network_interfaces": {},
        "dns_config": {},
        "routing": {}
    }
    
    # Informations système
    network_info["container_info"] = {
        "hostname": socket.gethostname(),
        "platform": platform.platform(),
        "python_version": platform.python_version(),
        "timestamp": datetime.utcnow().isoformat()
    }
    
    # Interfaces réseau
    try:
        interfaces = psutil.net_if_addrs()
        for interface_name, addresses in interfaces.items():
            network_info["network_interfaces"][interface_name] = []
            for addr in addresses:
                if addr.family == socket.AF_INET:  # IPv4
                    network_info["network_interfaces"][interface_name].append({
                        "type": "IPv4",
                        "address": addr.address,
                        "netmask": addr.netmask,
                        "broadcast": addr.broadcast
                    })
                elif addr.family == socket.AF_INET6:  # IPv6
                    network_info["network_interfaces"][interface_name].append({
                        "type": "IPv6",
                        "address": addr.address,
                        "netmask": addr.netmask
                    })
    except Exception as e:
        print(f"⚠️ Error collecting interfaces: {e}")
    
    # Configuration DNS
    try:
        # Résolution DNS du container
        container_ip = socket.gethostbyname(socket.gethostname())
        network_info["dns_config"]["container_ip"] = container_ip
        
        # DNS Servers
        try:
            result = subprocess.run(['cat', '/etc/resolv.conf'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                dns_servers = []
                for line in result.stdout.split('\n'):
                    if line.startswith('nameserver'):
                        dns_servers.append(line.split()[1])
                network_info["dns_config"]["nameservers"] = dns_servers
        except Exception as e:
            print(f"⚠️ Cannot read /etc/resolv.conf: {e}")
            
    except Exception as e:
        print(f"⚠️ Error collecting DNS info: {e}")
    
    # Routing table
    try:
        result = subprocess.run(['ip', 'route'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            network_info["routing"]["routes"] = result.stdout.strip().split('\n')
    except Exception as e:
        print(f"⚠️ Cannot retrieve routing table: {e}")
    
    return network_info

def test_connectivity(host, port=80, timeout=10):
    """TCP connectivity test to a host"""
    print(f"🔌 TCP connectivity test to {host}:{port}")
    
    try:
        start_time = time.time()
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        connect_time = (time.time() - start_time) * 1000
        sock.close()
        
        if result == 0:
            print(f"✅ TCP connectivity: {connect_time:.2f}ms")
            return {"status": "success", "time_ms": round(connect_time, 2)}
        else:
            print(f"❌ TCP connectivity failed (code: {result})")
            return {"status": "error", "error": f"Connection failed with code {result}"}
    except Exception as e:
        print(f"❌ TCP connectivity error: {e}")
        return {"status": "error", "error": str(e)}

def test_ping(host, count=3):
    """Ping test to a host"""
    print(f"🏓 Ping test to {host}")
    
    try:
        # Use Windows ping command on Windows
        if platform.system() == 'Windows':
            result = subprocess.run(['ping', '-n', str(count), host], 
                                  capture_output=True, text=True, timeout=30)
        else:
            result = subprocess.run(['ping', '-c', str(count), host], 
                                  capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            # Analyze ping output
            lines = result.stdout.split('\n')
            ping_stats = {}
            
            if platform.system() == 'Windows':
                # Windows ping output analysis
                for line in lines:
                    if 'Packets: Sent =' in line:
                        parts = line.split(',')
                        ping_stats["packets_transmitted"] = int(parts[0].split('=')[1].strip())
                        ping_stats["packets_received"] = int(parts[1].split('=')[1].strip())
                        ping_stats["packet_loss"] = parts[2].strip()
                    elif 'Minimum =' in line:
                        ping_stats["rtt_stats"] = line.strip()
            else:
                # Linux ping output analysis
                for line in lines:
                    if 'packets transmitted' in line:
                        parts = line.split(',')
                        ping_stats["packets_transmitted"] = int(parts[0].split()[0])
                        ping_stats["packets_received"] = int(parts[1].split()[0])
                        ping_stats["packet_loss"] = parts[2].strip()
                    elif 'min/avg/max' in line:
                        ping_stats["rtt_stats"] = line.split('=')[1].strip()
            
            print(f"✅ Ping successful: {ping_stats}")
            return {"status": "success", "stats": ping_stats}
        else:
            print(f"❌ Ping failed: {result.stderr}")
            return {"status": "error", "error": result.stderr}
    except Exception as e:
        print(f"❌ Ping error: {e}")
        return {"status": "error", "error": str(e)}

def test_url(url, timeout=30):
    """Detailed URL test with complete network information"""
    results = {
        "url": url,
        "timestamp": datetime.utcnow().isoformat(),
        "tests": {}
    }
    
    print(f"\n🚀 Detailed test of: {url}")
    print("=" * 60)
    
    # Parse URL
    try:
        parsed_url = urlparse(url)
        host = parsed_url.netloc
        port = parsed_url.port or (443 if parsed_url.scheme == 'https' else 80)
        print(f"📍 Host: {host}, Port: {port}, Protocol: {parsed_url.scheme}")
        
        # SAP-specific information
        if 'sap' in host.lower() or 'sap-client' in url:
            print(f"🔧 SAP URL detected - Client: {parsed_url.query}")
            results["sap_info"] = {
                "host": host,
                "port": port,
                "path": parsed_url.path,
                "query_params": parsed_url.query
            }
    except Exception as e:
        print(f"❌ URL parsing error: {e}")
        results["tests"]["url_parsing"] = {"status": "error", "error": str(e)}
        return results
    
    # Extract hostname without port for DNS and ping tests
    hostname = host.split(':')[0] if ':' in host else host
    
    # Detailed DNS test
    print(f"\n🔍 DNS test for {hostname}")
    try:
        start_time = time.time()
        ip = socket.gethostbyname(hostname)
        dns_time = (time.time() - start_time) * 1000
        
        # Reverse DNS lookup
        try:
            reverse_host = socket.gethostbyaddr(ip)[0]
        except:
            reverse_host = "Not available"
        
        results["tests"]["dns"] = {
            "status": "success",
            "ip": ip,
            "reverse_dns": reverse_host,
            "time_ms": round(dns_time, 2)
        }
        print(f"✅ DNS: {dns_time:.2f}ms → {ip}")
        print(f"   Reverse DNS: {reverse_host}")
    except Exception as e:
        results["tests"]["dns"] = {"status": "error", "error": str(e)}
        print(f"❌ DNS: {e}")
    
    # TCP connectivity test (use hostname without port for TCP test)
    tcp_host = hostname if ':' in host else host
    results["tests"]["tcp_connectivity"] = test_connectivity(tcp_host, port, timeout)
    
    # Ping test (if allowed)
    results["tests"]["ping"] = test_ping(hostname)
    
    # Detailed HTTP/HTTPS test
    print(f"\n🌐 HTTP/HTTPS test to {url}")
    try:
        start_time = time.time()
        
        # Custom headers for more information
        headers = {
            'User-Agent': 'Network-Tester/1.0',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'close'
        }
        
        response = requests.get(url, timeout=timeout, headers=headers, verify=False)
        response_time = (time.time() - start_time) * 1000
        
        # Detailed response information
        # Consider 401 (authentication required) as success for SAP systems
        is_success = response.status_code < 500 and response.status_code != 404
        http_info = {
            "status": "success" if is_success else "error",
            "status_code": response.status_code,
            "time_ms": round(response_time, 2),
            "content_length": len(response.content),
            "headers": dict(response.headers),
            "encoding": response.encoding,
            "url_final": response.url
        }
        
        # SSL information if HTTPS
        if parsed_url.scheme == 'https':
            try:
                ssl_info = response.raw.connection.sock.getpeercert()
                http_info["ssl"] = {
                    "subject": dict(x[0] for x in ssl_info['subject']),
                    "issuer": dict(x[0] for x in ssl_info['issuer']),
                    "version": ssl_info['version'],
                    "serial_number": ssl_info['serialNumber'],
                    "not_before": ssl_info['notBefore'],
                    "not_after": ssl_info['notAfter']
                }
            except Exception as e:
                http_info["ssl"] = {"error": str(e)}
        
        results["tests"]["http"] = http_info
        print(f"✅ HTTP: {response_time:.2f}ms (Status: {response.status_code})")
        print(f"   Content-Length: {len(response.content)} bytes")
        print(f"   Encoding: {response.encoding}")
        print(f"   Final URL: {response.url}")
        
    except requests.exceptions.SSLError as e:
        results["tests"]["http"] = {"status": "ssl_error", "error": str(e)}
        print(f"❌ Erreur SSL: {e}")
    except requests.exceptions.ConnectionError as e:
        results["tests"]["http"] = {"status": "connection_error", "error": str(e)}
        print(f"❌ Erreur de connexion: {e}")
    except requests.exceptions.Timeout as e:
        results["tests"]["http"] = {"status": "timeout", "error": str(e)}
        print(f"❌ Timeout: {e}")
    except Exception as e:
        results["tests"]["http"] = {"status": "error", "error": str(e)}
        print(f"❌ Erreur HTTP: {e}")
    
    return results

def main():
    print("🔧 Starting main function...")
    
    # URLs to test
    test_urls = [
        os.getenv('SAP_URL', 'https://vhinoev1ci.rise.apps.eon.com:44300/sap/bc/gui/sap/its/webgui/#'),  # Internal SAP URL
        'https://oneb-central-dev.launchpad.cfapps.eu10.hana.ondemand.com/site?siteId=1af3caee-8117-49e3-b787-7ad74914b5cb',  # SAP Launchpad URL to test
        'https://www.google.com'  # Internet connectivity test
    ]
    
    print(f"🔧 Test URLs: {test_urls}")
    
    timeout = int(os.getenv('TIMEOUT', '30'))
    print(f"🔧 Timeout: {timeout}")
    
    print("🌐 Network Connectivity Tester - Detailed Version")
    print("=" * 60)
    
    print("🔧 About to collect network info...")
    # Collect container network information
    network_info = get_container_network_info()
    print("🔧 Network info collected successfully")
    
    # Display network information
    print("\n📊 CONTAINER NETWORK INFORMATION")
    print("=" * 60)
    print(f"Hostname: {network_info['container_info']['hostname']}")
    print(f"Platform: {network_info['container_info']['platform']}")
    print(f"Container IP: {network_info['dns_config'].get('container_ip', 'Not available')}")
    
    if 'nameservers' in network_info['dns_config']:
        print(f"DNS Servers: {', '.join(network_info['dns_config']['nameservers'])}")
    
    print("\nNetwork interfaces:")
    for interface, addresses in network_info['network_interfaces'].items():
        print(f"  {interface}:")
        for addr in addresses:
            if addr['type'] == 'IPv4':
                print(f"    IPv4: {addr['address']}/{addr['netmask']}")
            else:
                print(f"    IPv6: {addr['address']}")
    
    # Connectivity tests
    all_results = {
        "network_info": network_info,
        "connectivity_tests": {}
    }
    
    for url in test_urls:
        if url:  # Test all valid URLs
            print(f"\n{'='*60}")
            results = test_url(url, timeout)
            all_results["connectivity_tests"][url] = results
    
    # Save results
    output_file = os.getenv('OUTPUT_FILE', 'detailed_network_test_results.json')
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Detailed results saved in: {output_file}")
    
    # Analyze results and diagnostics
    print(f"\n🔍 NETWORK DIAGNOSTICS")
    print("=" * 60)
    
    for url, results in all_results["connectivity_tests"].items():
        print(f"\nURL: {url}")
        
        # DNS
        dns_status = results.get("tests", {}).get("dns", {}).get("status")
        if dns_status == "success":
            print(f"  DNS: ✅ Resolution successful")
        else:
            print(f"  DNS: ❌ Resolution problem")
        
        # TCP
        tcp_status = results.get("tests", {}).get("tcp_connectivity", {}).get("status")
        if tcp_status == "success":
            print(f"  TCP: ✅ Connectivity successful")
        else:
            print(f"  TCP: ❌ Connectivity problem")
        
        # HTTP
        http_status = results.get("tests", {}).get("http", {}).get("status")
        if http_status == "success":
            print(f"  HTTP: ✅ Web access successful")
        elif http_status == "ssl_error":
            print(f"  HTTP: ⚠️ SSL problem")
        elif http_status == "connection_error":
            print(f"  HTTP: ❌ Connection error")
        else:
            print(f"  HTTP: ❌ Access problem")
    
    # Exit code based on general connectivity
    all_http_success = all(
        results.get("tests", {}).get("http", {}).get("status") == "success"
        for results in all_results["connectivity_tests"].values()
    )
    
    exit_code = 0 if all_http_success else 1
    print(f"\n🏁 Completed with code: {exit_code}")
    exit(exit_code)

if __name__ == "__main__":
    try:
        print("🚀 Starting network tester script...")
        main()
    except Exception as e:
        print(f"❌ Error in main: {e}")
        import traceback
        traceback.print_exc()
        exit(1)