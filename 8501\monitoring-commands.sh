#!/bin/bash

# Configuration
RESOURCE_GROUP="RG-EDGMON-RUN"
JOB_NAME="powercloud-monitoring-8500-job"
STORAGE_ACCOUNT="scriptsmonitoring2025"

echo "🔍 Monitoring Commands for Powercloud Monitoring Job"
echo "==================================================="

# Interactive menu
while true; do
    echo ""
    echo "Choose an option:"
    echo "1. View job status"
    echo "2. List recent executions"
    echo "3. View logs from latest execution"
    echo "4. Start job manually"
    echo "5. View result files"
    echo "6. Download CSV logs"
    echo "7. Modify schedule"
    echo "8. View performance metrics"
    echo "0. Exit"
    echo ""
    read -p "Your choice (0-8): " choice
    
    case $choice in
        1)
            echo "📊 Job status:"
            az containerapp job show --name $JOB_NAME --resource-group $RESOURCE_GROUP --output table
            ;;
        2)
            echo "📜 Recent executions:"
            az containerapp job execution list --name $JOB_NAME --resource-group $RESOURCE_GROUP --output table
            ;;
        3)
            echo "📝 Logs from latest execution:"
            LATEST_EXEC=$(az containerapp job execution list --name $JOB_NAME --resource-group $RESOURCE_GROUP --query "[0].name" -o tsv)
            if [ -n "$LATEST_EXEC" ]; then
                az containerapp job execution logs --name $JOB_NAME --resource-group $RESOURCE_GROUP --execution-name $LATEST_EXEC
            else
                echo "❌ No executions found"
            fi
            ;;
        4)
            echo "🚀 Starting job manually..."
            az containerapp job start --name $JOB_NAME --resource-group $RESOURCE_GROUP
            echo "✅ Job started"
            ;;
        5)
            echo "📁 Files in Azure Files:"
            az storage file list --account-name $STORAGE_ACCOUNT --share-name "script-monitoring" --path "8500" --output table
            ;;
        6)
            echo "💾 Downloading CSV results file..."
            mkdir -p ./downloads
            az storage file download --account-name $STORAGE_ACCOUNT --share-name "script-monitoring" --path "8500/8500_RTR.csv" --dest "./downloads/8500_RTR.csv"
            echo "✅ File downloaded to ./downloads/8500_RTR.csv"
            ;;
        7)
            echo "⏰ Current schedule:"
            CURRENT_SCHEDULE=$(az containerapp job show --name $JOB_NAME --resource-group $RESOURCE_GROUP --query "properties.schedule.cronExpression" -o tsv)
            echo "Current cron expression: $CURRENT_SCHEDULE"
            echo ""
            echo "Common schedules:"
            echo "- Every 5 minutes: */5 * * * *"
            echo "- Every 15 minutes: */15 * * * *"
            echo "- Every 30 minutes: */30 * * * *"
            echo "- Every hour: 0 * * * *"
            echo "- Every day at midnight: 0 0 * * *"
            echo ""
            read -p "Enter new cron expression (or press Enter to keep current): " NEW_SCHEDULE
            
            if [ -n "$NEW_SCHEDULE" ]; then
                az containerapp job update --name $JOB_NAME --resource-group $RESOURCE_GROUP --cron-expression "$NEW_SCHEDULE"
                echo "✅ Schedule updated"
            else
                echo "ℹ️ Schedule unchanged"
            fi
            ;;
        8)
            echo "📈 Performance metrics (recent executions):"
            # Download and analyze CSV file
            mkdir -p ./temp
            az storage file download --account-name $STORAGE_ACCOUNT --share-name "script-monitoring" --path "8500/8500_RTR.csv" --dest "./temp/metrics.csv" 2>/dev/null
            if [ -f "./temp/metrics.csv" ]; then
                echo ""
                echo "📊 Summary of recent measurements:"
                tail -10 ./temp/metrics.csv | awk -F';' 'NR>1 {
                    print "Date: " $2 " " $3
                    print "  - Response time: " $8 "ms"
                    print "  - Error: " $10
                    print "---"
                }'
                rm -f ./temp/metrics.csv
            else
                echo "❌ Unable to retrieve metrics"
            fi
            ;;
        0)
            echo "👋 Exiting..."
            exit 0
            ;;
        *)
            echo "❌ Invalid option"
            ;;
    esac
    
    echo ""
    read -p "Press Enter to continue..."
done