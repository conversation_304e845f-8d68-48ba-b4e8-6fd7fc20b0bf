FROM python:3.11-slim

WORKDIR /app

RUN apt-get update && apt-get install -y curl iputils-ping dnsutils && rm -rf /var/lib/apt/lists/*

RUN pip install --no-cache-dir requests==2.31.0 psutil==5.9.6 cryptography==41.0.7

# Copier le fichier spécifiquement
COPY web_tester.py /app/web_tester.py

# Vérifier qu'il est bien là
RUN ls -la /app/
RUN test -f /app/web_tester.py && echo "Fichier trouvé !" || echo "Fichier manquant !"

CMD ["python", "/app/web_tester.py"]