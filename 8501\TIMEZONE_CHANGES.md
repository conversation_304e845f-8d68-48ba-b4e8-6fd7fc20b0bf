# Timezone Handling Changes

## Problème résolu
Le script envoyait l'heure UTC dans Splunk même quand il tournait dans un conteneur Docker configuré avec le timezone Europe/Berlin.

## Solution implémentée

### 1. Détection automatique de l'environnement
- **Local** : Détecte quand le script tourne en local (pas de conteneur)
- **Conteneur** : D<PERSON>tecte quand le script tourne dans Docker/Kubernetes

### 2. Gestion des timezones par destination

#### CSV (fichier local)
- **Local** : Utilise l'heure locale du système
- **Conteneur** : Utilise l'heure de Berlin (Europe/Berlin)

#### Splunk
- **Local** : Utilise l'heure locale du système  
- **Conteneur** : Utilise l'heure de Berlin (Europe/Berlin)

#### New Relic
- **Toujours** : Convertit vers UTC (comme requis par New Relic)

### 3. Changements techniques

#### Nouvelle méthode `_detect_container_environment()`
Détecte l'environnement en vérifiant :
- Fichier `/.dockerenv` (Docker)
- Contenu de `/proc/1/cgroup` (Docker/conteneur)
- Variables d'environnement Kubernetes
- Variable `TZ=Europe/Berlin` (définie dans notre Dockerfile)

#### Nouvelle méthode `get_current_time()`
Retourne l'heure appropriée selon l'environnement :
- **Local** : `datetime.now()` (heure système)
- **Conteneur** : `datetime.now()` localisé en Europe/Berlin

#### Modification de `send_to_new_relic()`
Convertit toujours vers UTC :
- Parse le timestamp local
- Le localise dans le bon timezone si nécessaire
- Le convertit vers UTC pour New Relic

### 4. Configuration Docker
Le Dockerfile configure déjà correctement le timezone :
```dockerfile
ENV TZ=Europe/Berlin
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
```

### 5. Résultat attendu

#### En local (par exemple en France, UTC+2)
- CSV : 09:46:25 (heure locale française)
- Splunk : 09:46:25 (heure locale française)  
- New Relic : 07:46:25 (UTC)

#### En conteneur Azure (Berlin, UTC+2)
- CSV : 09:46:25 (heure de Berlin)
- Splunk : 09:46:25 (heure de Berlin)
- New Relic : 07:46:25 (UTC)

## Tests
Utilisez les scripts de test :
- `test_timezone.py` : Teste la détection d'environnement
- `test_timezone_conversion.py` : Teste les conversions de timezone
