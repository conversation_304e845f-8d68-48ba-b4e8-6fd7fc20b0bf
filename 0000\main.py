import os
import uuid
import datetime
from dotenv import load_dotenv
from azure.storage.fileshare import ShareServiceClient, ShareFileClient
from azure.identity import DefaultAzureCredential

# Load environment variables
load_dotenv()

# Configuration
STORAGE_ACCOUNT_NAME = os.getenv("STORAGE_ACCOUNT_NAME")
FILE_SHARE_NAME = os.getenv("FILE_SHARE_NAME")
USE_MANAGED_IDENTITY = os.getenv("USE_MANAGED_IDENTITY", "false").lower() == "true"

def write_to_csv():
    """
    Write a line with a random ID to a CSV file in Azure File Share
    """
    try:
        # Generate current date for filename
        current_date = datetime.datetime.utcnow().strftime("%Y%m%d")
        filename = f"testcsv_{current_date}.csv"
        
        # Generate random ID
        random_id = str(uuid.uuid4())
        
        # Current timestamp
        timestamp = datetime.datetime.utcnow().isoformat()
        
        # Create CSV line
        csv_line = f"{random_id},{timestamp}\n"
        
        print(f"Writing to file {filename}")
        print(f"Line content: {csv_line.strip()}")
        
        # Connect to Azure File Share
        if USE_MANAGED_IDENTITY:
            # Use managed identity
            credential = DefaultAzureCredential()
            service_client = ShareServiceClient(
                account_url=f"https://{STORAGE_ACCOUNT_NAME}.file.core.windows.net",
                credential=credential
            )
        else:
            # Use connection string
            connection_string = os.getenv("AZURE_STORAGE_CONNECTION_STRING")
            service_client = ShareServiceClient.from_connection_string(connection_string)
        
        # Get share client
        share_client = service_client.get_share_client(FILE_SHARE_NAME)
        
        # Check if file exists and create it with header if it doesn't
        file_client = share_client.get_file_client(filename)
        file_exists = True
        
        try:
            file_client.get_file_properties()
        except Exception:
            file_exists = False
        
        if not file_exists:
            print(f"File {filename} does not exist. Creating with header.")
            header = "id,timestamp\n"
            file_client.upload_file(header)
        
        # Append data to the file
        # Since Azure File Share doesn't support direct append, we need to download,
        # append locally, and upload again
        if file_exists:
            # Download current content
            download = file_client.download_file()
            content = download.readall().decode('utf-8')
            
            # Append new line
            content += csv_line
            
            # Upload updated content
            file_client.upload_file(content)
        else:
            # Add new line to the file we just created with header
            file_client.upload_file(header + csv_line, overwrite=True)
        
        print(f"Successfully wrote to {filename} in Azure File Share {FILE_SHARE_NAME}")
        return True
        
    except Exception as e:
        print(f"Error writing to CSV: {e}")
        return False

if __name__ == "__main__":
    write_to_csv()
    print("Process completed")
