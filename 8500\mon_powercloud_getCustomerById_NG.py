
import os
import sys
import time
import json
import csv
import uuid
import requests
import logging
import pytz
import urllib3
import ssl
from datetime import datetime
from azure.keyvault.secrets import SecretClient
from azure.identity import DefaultAzureCredential
from azure.storage.fileshare import ShareFileClient, ShareDirectoryClient

# Disable SSL warnings and certificate verification
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

# Azure configuration constants
KEYVAULT_NAME = "scriptsmonitoring2025"

# Disable logs from Azure SDK libraries
logging.getLogger('azure').setLevel(logging.ERROR)
logging.getLogger('azure.identity').setLevel(logging.ERROR)
logging.getLogger('azure.core').setLevel(logging.ERROR)
logging.getLogger('azure.keyvault').setLevel(logging.ERROR)
logging.getLogger('azure.storage').setLevel(logging.ERROR)
logging.getLogger('azure.storage.fileshare').setLevel(logging.ERROR)
logging.getLogger('azure.core.pipeline').setLevel(logging.ERROR)
logging.getLogger('azure.core.pipeline.policies').setLevel(logging.ERROR)
logging.getLogger('msal').setLevel(logging.ERROR)
logging.getLogger('urllib3').setLevel(logging.ERROR)
logging.getLogger('requests').setLevel(logging.ERROR)

class PowercloudMonitoring:
    def __init__(self):
        """Initialize the monitoring application"""
        self.script_id = "8500"

        # Detect if running in container or locally
        self.is_container = self._detect_container_environment()

        # Set timezone based on environment
        if self.is_container:
            self.timezone = pytz.timezone('Europe/Berlin')
            print("🐳 Running in container - using Europe/Berlin timezone")
        else:
            # Use local system timezone
            self.timezone = None  # Will use system local timezone
            print("💻 Running locally - using system local timezone")

        # Local paths
        self.local_results_path = os.path.join(os.getcwd(), "results")
        os.makedirs(self.local_results_path, exist_ok=True)
        
        # Azure configuration
        self.storage_account_name = "scriptsmonitoring2025"
        self.file_share_name = "script-monitoring"
        self.container_name = self.script_id

        # SAS Token for Azure File Storage (same as working 3110 script)
        self.sas_token = "sv=2024-11-04&ss=bfqt&srt=sco&sp=rwlacupiytfx&se=2099-05-27T16:12:28Z&st=2025-05-27T08:12:28Z&spr=https&sig=aC8CDIMg0AP4NbymnSwflWBaGKG7sFpswl%2FcslzuqPY%3D"

        # Azure authentication for Key Vault (keep DefaultAzureCredential for Key Vault)
        self.credential = DefaultAzureCredential()
        
        # Key Vault configuration
        self.keyvault_url = f"https://{KEYVAULT_NAME}.vault.azure.net/"
        self.secret_client = SecretClient(vault_url=self.keyvault_url, credential=self.credential)
        
        # Splunk HEC Configuration
        self.splunk_hec_url = "https://splunk-hec.eon.com:443/services/collector/event"
        self.splunk_token = "bbab61ff-f874-4e71-8403-257370da6485"
        self.splunk_sourcetype = "http_eek_ng_monitoring"
        self.splunk_index = "idx_eek_robots"
        self.splunk_host = f"azure_mon_{self.script_id}"
        
        # Test Azure connectivity first (like the working 3110 script)
        print("🔐 Testing Azure connectivity...")
        test_secret = self.get_secret("eon-email")
        if test_secret:
            print("✅ Azure Key Vault connectivity confirmed")
        else:
            print("⚠️ Azure Key Vault connectivity issue - using fallback values")

        self.new_relic_api_key = self.get_secret(f"{self.script_id}-new-relic-api-key") or "eu01xx0a982ff080c0b184b6137750a8FFFFNRAL"
        self.new_relic_log_api_url = "https://log-api.eu.newrelic.com/log/v1"

        print(f"PowercloudMonitoring initialized for script {self.script_id}")

    def _detect_container_environment(self):
        """Detect if running in a container environment"""
        # Check for common container indicators
        container_indicators = []

        try:
            # Docker environment file
            if os.path.exists('/.dockerenv'):
                container_indicators.append(True)
                print("🐳 Detected Docker environment file")

            # Check cgroup for docker/container
            if os.path.exists('/proc/1/cgroup'):
                with open('/proc/1/cgroup', 'r') as f:
                    cgroup_content = f.read()
                    if 'docker' in cgroup_content or 'container' in cgroup_content:
                        container_indicators.append(True)
                        print("🐳 Detected container in cgroup")

            # Kubernetes environment
            if os.environ.get('KUBERNETES_SERVICE_HOST'):
                container_indicators.append(True)
                print("🐳 Detected Kubernetes environment")

            # Generic container environment variables
            if os.environ.get('CONTAINER_NAME') or os.environ.get('DOCKER_CONTAINER'):
                container_indicators.append(True)
                print("🐳 Detected container environment variables")

            # Check TZ environment variable (set in our Dockerfile)
            if os.environ.get('TZ') == 'Europe/Berlin':
                container_indicators.append(True)
                print("🐳 Detected Berlin timezone environment variable")

            return any(container_indicators)
        except Exception as e:
            print(f"⚠️ Error detecting container environment: {e}")
            # If any check fails, assume local environment
            return False

    def get_current_time(self):
        """Get current time in the appropriate timezone"""
        if self.is_container:
            # In container, datetime.now() should already be in Berlin time due to TZ=Europe/Berlin
            # But let's be explicit about it
            current_time = datetime.now()
            if current_time.tzinfo is None:
                # If naive, assume it's already in the container's timezone (Berlin)
                current_time = self.timezone.localize(current_time)
            print(f"🐳 Container time: {current_time} ({current_time.tzinfo})")
            return current_time
        else:
            # Local environment - use system local time
            current_time = datetime.now()
            print(f"💻 Local time: {current_time}")
            return current_time

    def get_secret(self, secret_name):
        """Get a secret from Azure Key Vault"""
        try:
            print(f"Retrieving secret: {secret_name}")
            secret = self.secret_client.get_secret(secret_name)
            print(f"✅ Secret {secret_name} retrieved successfully")
            return secret.value
        except Exception as e:
            print(f"❌ Failed to get secret {secret_name}: {str(e)}")
            # Return fallback values for specific secrets (with and without prefix)
            fallback_values = {
                "new-relic-api-key": "eu01xx0a982ff080c0b184b6137750a8FFFFNRAL",
                "8500-new-relic-api-key": "eu01xx0a982ff080c0b184b6137750a8FFFFNRAL",
                "powercloud-auth-hash": "ebf7e5994adefcaa0aa2d8b80a562d39",
                "8500-powercloud-auth-hash": "ebf7e5994adefcaa0aa2d8b80a562d39",
                "powercloud-auth-header": "Basic Y2xpZW50I2VvbjpKLExMe19CNCI3cnIiLFt1",
                "8500-powercloud-auth-header": "Basic Y2xpZW50I2VvbjpKLExMe19CNCI3cnIiLFt1",
                "powercloud-customer-id": "200814519",
                "8500-powercloud-customer-id": "200814519"
            }
            if secret_name in fallback_values:
                print(f"Using fallback value for {secret_name}")
                return fallback_values[secret_name]
            return None

    def create_azure_directory_if_not_exists(self, directory_path):
        """Creates directory structure in Azure File Share if it doesn't exist"""
        try:
            # Split the path into components
            path_parts = directory_path.strip('/').split('/')
            current_path = ""

            # Create each directory level if needed
            for part in path_parts:
                if part:
                    if current_path:
                        current_path = f"{current_path}/{part}"
                    else:
                        current_path = part

                    # Create directory client using SAS Token
                    directory_client = ShareDirectoryClient(
                        account_url=f"https://{self.storage_account_name}.file.core.windows.net",
                        share_name=self.file_share_name,
                        directory_path=current_path,
                        credential=self.sas_token,
                        connection_verify=False
                    )

                    # Create directory if it doesn't exist
                    if not directory_client.exists():
                        directory_client.create_directory()
                        print(f"✅ Created directory: {current_path}")

            return True
        except Exception as e:
            print(f"❌ Error creating directory {directory_path}: {str(e)}")
            return False

    def upload_to_azure_storage(self, local_path, remote_path):
        """Upload a file to Azure Storage using SAS Token"""
        try:
            print(f"🔍 DEBUGGING AZURE UPLOAD")
            print(f"   Local Path: {local_path}")
            print(f"   Remote Path: {remote_path}")

            # Verify local file before upload
            if not os.path.exists(local_path):
                print(f"❌ Local file does not exist: {local_path}")
                return False

            local_file_size = os.path.getsize(local_path)
            print(f"📊 Local file size: {local_file_size} bytes")

            # Show local file content for debugging
            with open(local_path, 'r', encoding='utf-8') as f:
                local_lines = f.readlines()
                print(f"📊 Local file has {len(local_lines)} lines before upload")
                if len(local_lines) > 0:
                    print(f"📄 First line: {local_lines[0].strip()}")
                if len(local_lines) > 1:
                    print(f"📄 Last line: {local_lines[-1].strip()}")

            # Create parent directories if they don't exist
            directory_path = os.path.dirname(remote_path)
            if directory_path:
                self.create_azure_directory_if_not_exists(directory_path)

            # Use SAS Token instead of DefaultAzureCredential to avoid SSL issues
            share_file_client = ShareFileClient(
                account_url=f"https://{self.storage_account_name}.file.core.windows.net",
                share_name=self.file_share_name,
                file_path=remote_path,
                credential=self.sas_token,
                connection_verify=False  # Disable SSL verification
            )

            print(f"📤 Uploading file to Azure Storage: {remote_path}")
            with open(local_path, "rb") as source_file:
                file_content = source_file.read()
                print(f"📊 Uploading {len(file_content)} bytes")
                share_file_client.upload_file(file_content)

            print(f"✅ File uploaded to Azure: {remote_path}")

            # Verify upload by checking file properties
            try:
                file_properties = share_file_client.get_file_properties()
                print(f"📊 Uploaded file size on Azure: {file_properties.size} bytes")
                print(f"📊 Upload timestamp: {file_properties.last_modified}")

                # Verify the content by downloading and checking
                print(f"🔍 Verifying upload by re-downloading...")
                verify_content = share_file_client.download_file().readall()
                verify_lines = verify_content.decode('utf-8').split('\n')
                print(f"📊 Verified: Azure file has {len([l for l in verify_lines if l.strip()])} non-empty lines")
            except Exception as verify_error:
                print(f"⚠️ Could not verify upload: {verify_error}")
                return False

            return True
        except Exception as e:
            print(f"❌ Failed to upload file to Azure: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return False

    def download_existing_csv_from_azure(self, remote_path, local_path):
        """Download existing CSV file from Azure if it exists"""
        try:
            print(f"🔍 DEBUGGING AZURE DOWNLOAD")
            print(f"   Storage Account: {self.storage_account_name}")
            print(f"   File Share: {self.file_share_name}")
            print(f"   Remote Path: {remote_path}")
            print(f"   Local Path: {local_path}")
            print(f"   SAS Token: {self.sas_token[:20]}...")

            share_file_client = ShareFileClient(
                account_url=f"https://{self.storage_account_name}.file.core.windows.net",
                share_name=self.file_share_name,
                file_path=remote_path,
                credential=self.sas_token,
                connection_verify=False
            )

            print(f"🔍 Checking if file exists on Azure...")
            try:
                # Try to get file properties to check if file exists
                file_properties = share_file_client.get_file_properties()
                file_exists = True
                print(f"📊 File exists on Azure: {file_exists}")
                print(f"📊 File size on Azure: {file_properties.size} bytes")
                print(f"📊 Last modified: {file_properties.last_modified}")
            except Exception as check_error:
                file_exists = False
                print(f"📊 File exists on Azure: {file_exists}")
                print(f"📄 File not found: {str(check_error)}")

            if file_exists:
                print(f"📥 Downloading existing CSV from Azure: {remote_path}")

                # Download the file
                with open(local_path, "wb") as download_file:
                    download_stream = share_file_client.download_file()
                    content = download_stream.readall()
                    download_file.write(content)
                    print(f"📊 Downloaded {len(content)} bytes")

                # Verify downloaded file
                if os.path.exists(local_path):
                    file_size = os.path.getsize(local_path)
                    print(f"📊 Local file size: {file_size} bytes")

                    # Read and show content for debugging
                    with open(local_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        print(f"📊 Downloaded file has {len(lines)} lines")
                        if len(lines) > 0:
                            print(f"📄 First line: {lines[0].strip()}")
                        if len(lines) > 1:
                            print(f"📄 Last line: {lines[-1].strip()}")

                print(f"✅ Existing CSV downloaded to: {local_path}")
                return True
            else:
                print(f"📄 No existing CSV found in Azure: {remote_path}")
                return False
        except Exception as e:
            print(f"⚠️ Could not download existing CSV: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return False

    def trim_csv_file(self, file_path, max_lines=100000):
        """Trim CSV file to keep only the most recent max_lines entries"""
        try:
            if not os.path.exists(file_path):
                return True

            # Read all lines
            with open(file_path, 'r', encoding='utf-8') as file:
                lines = file.readlines()

            total_lines = len(lines)
            print(f"📊 CSV file has {total_lines} lines (including header)")

            if total_lines <= max_lines:
                print(f"✅ File size OK: {total_lines} <= {max_lines} lines")
                return True

            # Keep header + most recent (max_lines - 1) data lines
            lines_to_keep = max_lines - 1  # -1 for header
            print(f"🗑️ Trimming file: keeping header + {lines_to_keep} most recent lines")

            # Keep header (line 0) + last N data lines
            trimmed_lines = [lines[0]] + lines[-(lines_to_keep):]

            # Write trimmed file
            with open(file_path, 'w', encoding='utf-8') as file:
                file.writelines(trimmed_lines)

            print(f"✅ File trimmed from {total_lines} to {len(trimmed_lines)} lines")
            return True

        except Exception as e:
            print(f"⚠️ Error trimming CSV file: {str(e)}")
            return False

    def log_to_csv(self, data):
        """Save results to a local CSV file and upload to Azure"""
        try:
            print("Saving results to CSV...")

            # Create local file path
            local_file_path = os.path.join(self.local_results_path, f"{self.script_id}_RTR.csv")
            remote_path = f"{self.container_name}/{self.script_id}_RTR.csv"

            print(f"Local CSV path: {local_file_path}")
            print(f"Remote Azure path: {remote_path}")

            # STEP 1: Clean up any existing local file to start fresh
            if os.path.exists(local_file_path):
                os.remove(local_file_path)
                print(f"🗑️ Removed existing local file: {local_file_path}")

            # STEP 2: Try to download existing CSV from Azure
            download_success = self.download_existing_csv_from_azure(remote_path, local_file_path)

            # STEP 3: Check if we now have a local file (either downloaded or new)
            file_exists_after_download = os.path.exists(local_file_path)
            print(f"📊 File exists after download attempt: {file_exists_after_download}")

            # STEP 4: If we have a file, check its size and trim if necessary
            if file_exists_after_download:
                # Count lines before trim
                with open(local_file_path, 'r', encoding='utf-8') as f:
                    lines_before = len(f.readlines())
                print(f"📊 Downloaded file has {lines_before} lines")

                # Trim if necessary
                self.trim_csv_file(local_file_path, max_lines=100000)

                # Count lines after trim
                with open(local_file_path, 'r', encoding='utf-8') as f:
                    lines_after = len(f.readlines())
                print(f"📊 File after trim has {lines_after} lines")

            # STEP 5: Add new line to the file
            # Use 'a' mode to append, but we know the file state now
            with open(local_file_path, mode='a', newline='', encoding='utf-8') as file:
                writer = csv.DictWriter(file, fieldnames=data.keys(), delimiter=';')

                # Add header only if file is empty/new
                if not file_exists_after_download:
                    print("📝 Creating CSV header (new file)")
                    writer.writeheader()
                else:
                    print("📝 Appending to existing CSV (no header)")

                writer.writerow(data)

            # STEP 6: Verify final file content
            with open(local_file_path, 'r', encoding='utf-8') as f:
                final_lines = len(f.readlines())
            print(f"✅ Final local file has {final_lines} lines")

            # STEP 7: Upload updated file to Azure
            print("📤 Uploading updated file to Azure")
            upload_success = self.upload_to_azure_storage(local_file_path, remote_path)

            if upload_success:
                print(f"✅ CSV successfully updated on Azure with {final_lines} lines")
            else:
                print(f"❌ Failed to upload CSV to Azure")

            return upload_success
        except Exception as e:
            print(f"❌ Failed to log results: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return False

    def log_to_database(self, response_time, result_err):
        """Enregistre les résultats dans la base de données Azure SQL"""
        try:
            # Récupérer les credentials depuis Key Vault
            db_server = self.get_secret("db-server")
            db_name = self.get_secret("db-name")
            db_username = self.get_secret("db-username")
            db_password = self.get_secret("db-password")
            
            if not all([db_server, db_name, db_username, db_password]):
                print("Database credentials not found in Key Vault")
                return False
            
            # Connection string pour Azure SQL Database
            conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={db_server};DATABASE={db_name};UID={db_username};PWD={db_password};Encrypt=yes;TrustServerCertificate=no;Connection Timeout=30;'
            
            conn = pyodbc.connect(conn_str)
            cursor = conn.cursor()

            MID = str(uuid.uuid4())
            TIMESTAMP = datetime.now().strftime("%Y-%m-%d %H:%M:%S") + ".000"

            # Insérer dans la table Measurement
            sql_measurement = "INSERT INTO Measurement (MID,SCRIPTID,TIMESTAMP,STATIONID) VALUES (?,?,?,?)"
            cursor.execute(sql_measurement, (MID, self.script_id, TIMESTAMP, 200))
            
            # Insérer dans la table RESULT
            sql_result = "INSERT INTO RESULT (MID,NAME,VALUE) VALUES (?,?,?)"
            
            # Insérer le temps de réponse
            if response_time > 0:
                cursor.execute(sql_result, (MID, 'RESPONSE_TIME', str(response_time)))
            
            # Insérer le statut d'erreur
            cursor.execute(sql_result, (MID, 'ERROR', str(result_err)))
            
            conn.commit()
            conn.close()
            
            print(f"Results logged to database: MID={MID}, RESPONSE_TIME={response_time}, ERROR={result_err}")
            return True
        except Exception as e:
            print(f"Failed to log to database: {str(e)}")
            return False

    def send_to_splunk(self, event_data):
        """Send monitoring data to Splunk HEC"""
        try:
            print("Sending data to Splunk HEC...")

            # Convert date/time to epoch timestamp with appropriate timezone
            if "timestamp" in event_data and event_data["timestamp"]:
                # Use the ISO timestamp if available
                try:
                    if isinstance(event_data["timestamp"], str):
                        # Parse ISO timestamp (should already be in correct timezone)
                        timestamp_dt = datetime.fromisoformat(event_data["timestamp"].replace('Z', '+00:00'))
                        epoch_time = timestamp_dt.timestamp()
                        print(f"🕐 Using ISO timestamp: {event_data['timestamp']} -> {epoch_time}")
                    else:
                        raise ValueError("Invalid timestamp format")
                except:
                    # Fallback to date/time parsing
                    timestamp_str = f"{event_data['date']} {event_data['time']}"
                    timestamp_dt = datetime.strptime(timestamp_str, "%d.%m.%Y %H:%M:%S")
                    if self.timezone:
                        # Use specified timezone (container)
                        timestamp_dt = self.timezone.localize(timestamp_dt)
                        print(f"🕐 Using parsed date/time with {self.timezone}: {timestamp_str} -> {timestamp_dt.timestamp()}")
                    else:
                        # Use local timezone (local run)
                        print(f"🕐 Using parsed date/time with local timezone: {timestamp_str}")
                    epoch_time = timestamp_dt.timestamp()
            else:
                # Fallback to date/time parsing
                timestamp_str = f"{event_data['date']} {event_data['time']}"
                timestamp_dt = datetime.strptime(timestamp_str, "%d.%m.%Y %H:%M:%S")
                if self.timezone:
                    # Use specified timezone (container)
                    timestamp_dt = self.timezone.localize(timestamp_dt)
                    print(f"🕐 Using parsed date/time with {self.timezone}: {timestamp_str} -> {timestamp_dt.timestamp()}")
                else:
                    # Use local timezone (local run)
                    print(f"🕐 Using parsed date/time with local timezone: {timestamp_str}")
                epoch_time = timestamp_dt.timestamp()

            # Build Splunk event in required format
            splunk_event = {
                "time": epoch_time,
                "host": self.splunk_host,
                "sourcetype": self.splunk_sourcetype,
                "index": self.splunk_index,
                "event": {
                    "timestamp": event_data["timestamp"],
                    "date": event_data["date"],
                    "event_time": event_data["time"],
                    "script_id": event_data["script_id"],
                    "response_time": event_data["response_time"],
                    "error": event_data["error"],
                    "success": event_data["success"],
                    "type": "Powercloud API Monitoring"
                }
            }

            # Headers for Splunk HEC
            headers = {
                "Authorization": f"Splunk {self.splunk_token}",  # Timezone automatically detected (container vs local)
                "Content-Type": "application/json"
            }

            print("📦 Payload:")
            payload_str = json.dumps(splunk_event, indent=2)
            print(f"{payload_str}")
            print("=" * 60)

            # Send event to Splunk
            print("🚀 Sending request to Splunk HEC...")
            response = requests.post(
                self.splunk_hec_url,
                headers=headers,
                json=splunk_event,
                verify=False,  # Disable SSL verification
                timeout=30
            )

            # Detailed log of Splunk HEC response
            print("=" * 60)
            print("📥 SPLUNK HEC RESPONSE DETAILS")
            print("=" * 60)
            print(f"📊 Status Code: {response.status_code}")
            print("📋 Response Headers:")
            for key, value in response.headers.items():
                print(f"    '{key}': '{value}'")

            print(f"📄 Response Body:")
            try:
                if response.text:
                    try:
                        response_json = response.json()
                        print(json.dumps(response_json, indent=2))
                    except:
                        print(f"{response.text}")
                else:
                    print("(Empty response body)")
            except Exception as resp_error:
                print(f"⚠️ Could not read response body: {str(resp_error)}")

            print("=" * 60)

            if response.status_code == 200:
                print("✅ Event sent to Splunk successfully")
                return True
            else:
                print(f"❌ Splunk HEC error: {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ Error sending event to Splunk: {str(e)}")
            return False

    def send_to_new_relic(self, event_data):
        """Sends event data to New Relic Logs API"""
        try:
            print("Sending data to New Relic...")

            # New Relic Log API settings
            new_relic_log_api_url = 'https://log-api.eu.newrelic.com/log/v1'
            new_relic_api_key = 'eu01xx0a982ff080c0b184b6137750a8FFFFNRAL'

            # Convert timestamp to UTC epoch for New Relic (New Relic expects UTC)
            from datetime import datetime
            import time

            # Parse the local timestamp and convert to UTC
            if isinstance(event_data["timestamp"], str):
                # Parse ISO timestamp
                dt_obj = datetime.fromisoformat(event_data["timestamp"].replace('Z', '+00:00'))
                if dt_obj.tzinfo is None:
                    # If naive, assume it's in local timezone and convert to UTC
                    if self.timezone:
                        # Container: localize to Berlin then convert to UTC
                        dt_obj = self.timezone.localize(dt_obj)
                    # Convert to UTC
                    dt_obj = dt_obj.astimezone(pytz.UTC) if dt_obj.tzinfo else dt_obj
                else:
                    # Already has timezone info, convert to UTC
                    dt_obj = dt_obj.astimezone(pytz.UTC)
                epoch_time = str(int(dt_obj.timestamp()))
                print(f"🕐 New Relic timestamp (UTC): {dt_obj.isoformat()} -> {epoch_time}")
            else:
                # Fallback to current time in UTC
                epoch_time = str(int(time.time()))

            # Prepare data for New Relic with specified format
            new_relic_payload = {
                "timestamp": epoch_time,
                "message": f"EON Monitoring Event - script {event_data['script_id']}",
                "attributes": {
                    "date": event_data["date"],
                    "event_time": event_data["time"],
                    "script_id": event_data["script_id"],
                    "response_time": event_data["response_time"],
                    "error": event_data["error"],
                    "success": event_data["success"],
                    "type": "Powercloud API Monitoring",
                    "source": f"azure_mon_{self.script_id}"
                }
            }

            # Headers pour New Relic
            headers = {
                "Api-Key": new_relic_api_key,
                "Content-Type": "application/json"
            }

            print("📦 Payload:")
            payload_str = json.dumps(new_relic_payload, indent=2)
            print(f"{payload_str}")
            print("=" * 60)

            # Envoyer les données à New Relic
            print("🚀 Sending request to New Relic Logs API...")
            response = requests.post(
                new_relic_log_api_url,
                headers=headers,
                json=new_relic_payload,
                verify=False,  # Désactiver la vérification SSL
                timeout=30
            )

            # Log détaillé de la réponse New Relic
            print("=" * 60)
            print("📥 NEW RELIC API RESPONSE DETAILS")
            print("=" * 60)
            print(f"📊 Status Code: {response.status_code}")
            print("📋 Response Headers:")
            for key, value in response.headers.items():
                print(f"    '{key}': '{value}'")

            print(f"📄 Response Body:")
            try:
                if response.text:
                    try:
                        response_json = response.json()
                        print(json.dumps(response_json, indent=2))
                    except:
                        print(f"{response.text}")
                else:
                    print("(Empty response body)")
            except Exception as resp_error:
                print(f"⚠️ Could not read response body: {str(resp_error)}")

            print("=" * 60)

            if response.status_code in [200, 202]:  # New Relic renvoie généralement 202 Accepted
                print("✅ Event sent to New Relic successfully")
                return True
            else:
                print(f"❌ New Relic API error: {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ Error sending event to New Relic: {str(e)}")
            return False

    def check_api(self):
        """Check Powercloud getCustomerById API"""
        print("=" * 60)
        print("🚀 STARTING POWERCLOUD API CHECK")
        print("=" * 60)
        
        # Get credentials from Key Vault (using correct secret names with prefix)
        auth_hash = self.get_secret(f"{self.script_id}-powercloud-auth-hash") or "ebf7e5994adefcaa0aa2d8b80a562d39"
        auth_header = self.get_secret(f"{self.script_id}-powercloud-auth-header") or "Basic Y2xpZW50I2VvbjpKLExMe19CNCI3cnIiLFt1"
        customer_id = self.get_secret(f"{self.script_id}-powercloud-customer-id") or "200814519"
        
        # API configuration
        api_url = f"https://power.billify.eon.com/rest:client/{auth_hash}/getCustomerById?id={customer_id}&limit=500&offset=0"
        headers = {
            "Authorization": auth_header
        }
        
        print(f"API URL: {api_url}")
        print("API Headers: Authorization: ********")

        script_start_time = self.get_current_time()
        start_time = time.time()
        
        try:
            print(f"Calling API: {api_url}")
            response = requests.get(api_url, headers=headers, verify=False, timeout=30)
            response_time = int((time.time() - start_time) * 1000)  # Time in milliseconds

            script_end_time = self.get_current_time()
            
            # Log API response details
            print("📥 API RESPONSE DETAILS")
            print(f"📊 Status Code: {response.status_code}")
            print(f"⏱️ Response Time: {response_time}ms")
            
            # Check if response contains "Schmid"
            contains_schmid = "Schmid" in response.text
            print(f"🔍 Contains 'Schmid': {contains_schmid}")
            
            if contains_schmid:
                result_err = 0
                success = True
                print(f"✅ API check successful: Response time {response_time}ms")
            else:
                result_err = 1
                success = False
                print(f"❌ API check failed: Expected content not found. Status code: {response.status_code}")
            
            result_min = result_max = result_avg = response_time
            if result_err == 1:
                result_min = result_max = result_avg = -1
                
        except requests.exceptions.RequestException as e:
            script_end_time = self.get_current_time()
            response_time = -1
            result_min = result_max = result_avg = -1
            result_err = 1
            success = False
            print(f"❌ API request failed: {str(e)}")
        
        # Prepare data for CSV (use local time - system local or Berlin depending on environment)
        data = {
            "SCRIPT_ID": self.script_id,
            "SCRIPT_DATE": script_start_time.strftime("%d.%m.%Y"),
            "SCRIPT_START_TIME": script_start_time.strftime("%H:%M:%S"),
            "SCRIPT_END_TIME": script_end_time.strftime("%H:%M:%S"),
            "STATION_ID": 200,
            "STATION_SITE": "AWS",
            "RESULT_MIN": result_min,
            "RESULT_MAX": result_max,
            "RESULT_AVG": result_avg,
            "RESULT_ERR": result_err,
            "RESPONSE_TIME": response_time
        }

        # Save results to CSV (uses local time)
        print("📝 Saving results to CSV with local time")
        self.log_to_csv(data)
        
        # Prepare data for Splunk and New Relic (script_start_time already has correct timezone)
        target_time = script_start_time
        print(f"⏰ Using timestamp for monitoring data: {target_time} (timezone: {target_time.tzinfo if target_time.tzinfo else 'system local'})")

        detailed_data = {
            "timestamp": target_time.isoformat(),
            "date": target_time.strftime("%d.%m.%Y"),
            "time": target_time.strftime("%H:%M:%S"),
            "script_id": self.script_id,
            "response_time": response_time,
            "error": result_err,
            "success": success,
            "type": "Powercloud API Monitoring"
        }

        # Send data to monitoring platforms
        print("📤 Sending data to monitoring platforms")
        print("   📊 Splunk: will use local time (system local or Berlin)")
        print("   📈 New Relic: will convert to UTC")
        self.send_to_splunk(detailed_data)
        self.send_to_new_relic(detailed_data)
        
        print("=" * 60)
        print(f"✅ POWERCLOUD API CHECK COMPLETED: Error={result_err}, Response time={response_time}ms")
        print("=" * 60)
        
        return data

def main():
    """Main entry point"""
    try:
        print("=" * 60)
        print("🚀 STARTING POWERCLOUD MONITORING APPLICATION")
        print("=" * 60)
        
        monitoring = PowercloudMonitoring()
        result = monitoring.check_api()
        
        print(f"Powercloud Monitoring completed: Error={result['RESULT_ERR']}, Response time={result['RESULT_AVG']}ms")
        
        # Exit code based on result
        if result['RESULT_ERR'] == 0:
            print("Exiting with success code 0")
            print("Daten in CSV-Datei geschrieben.")
            sys.exit(0)  # Success
        else:
            print("Exiting with error code 1")
            print("Fehler beim Ausführen des Skripts.")
            sys.exit(1)  # Error
            
    except Exception as e:
        print(f"Application failed: {str(e)}")
        import traceback
        print(traceback.format_exc())
        print("Fehler beim Ausführen des Skripts.")
        sys.exit(1)

if __name__ == "__main__":
    main()
