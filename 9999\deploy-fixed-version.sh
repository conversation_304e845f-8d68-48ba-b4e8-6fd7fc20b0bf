#!/bin/bash

# Azure Container Deployment Script - Fixed Version
# Using your existing ACR infrastructure

set -e

# Configuration - Using your existing setup
RESOURCE_GROUP="RG-EDGMON-RUN"
ACR_NAME="scriptmonregistry2025"
ACR_LOGIN_SERVER="scriptmonregistry2025.azurecr.io"
ACR_USERNAME="SCRIPTMONREGISTRY2025"
ACR_PASSWORD="****************************************************"
CONTAINER_NAME="network-tester-fixed-$(date +%s)"
IMAGE_NAME="network-tester-fixed:latest"

echo "🚀 Starting Azure deployment with fixed version..."

# Build the Docker image
echo "🔨 Building Docker image..."
docker build -t $IMAGE_NAME .

echo "📤 Tagging image for ACR..."
docker tag $IMAGE_NAME $ACR_LOGIN_SERVER/$IMAGE_NAME

echo "🔐 Logging into ACR..."
echo $ACR_PASSWORD | docker login $ACR_LOGIN_SERVER -u $ACR_USERNAME --password-stdin

echo "📤 Pushing image to ACR..."
docker push $ACR_LOGIN_SERVER/$IMAGE_NAME

# Create Container Instance
echo "🚀 Creating Azure Container Instance..."
az container create \
    --resource-group $RESOURCE_GROUP \
    --name $CONTAINER_NAME \
    --image $ACR_LOGIN_SERVER/$IMAGE_NAME \
    --cpu 1 \
    --memory 1 \
    --os-type Linux \
    --restart-policy Never \
    --registry-login-server $ACR_LOGIN_SERVER \
    --registry-username $ACR_USERNAME \
    --registry-password "$ACR_PASSWORD" \
    --environment-variables \
        SAP_URL="https://vhinoev1ci.rise.apps.eon.com:44300/sap/bc/gui/sap/its/webgui/#" \
        TIMEOUT="60" \
        OUTPUT_FILE="/app/azure_sap_connectivity_test.json"

echo "✅ Container deployed successfully!"
echo "📋 Container name: $CONTAINER_NAME"

# Wait a moment for container to start
echo "⏳ Waiting for container to complete..."
sleep 30

# Get container logs
echo "📋 Container logs:"
az container logs --resource-group $RESOURCE_GROUP --name $CONTAINER_NAME

echo "🔍 Container status:"
az container show --resource-group $RESOURCE_GROUP --name $CONTAINER_NAME --query "{Status:provisioningState,RestartCount:containers[0].instanceView.restartCount,State:containers[0].instanceView.currentState}" --output table

echo "🎉 Deployment complete! Check the logs above for SAP connectivity test results." 