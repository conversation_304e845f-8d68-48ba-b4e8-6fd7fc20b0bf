# Azure Container Monitor Script - PowerShell Version
# Monitor execution and retrieve logs from Azure Container Instances

param(
    [string]$Action = "menu",
    [string]$ContainerName = ""
)

# Configuration - Using your existing setup
$ResourceGroup = "RG-EDGMON-RUN"

Write-Host "🔍 Azure Container Monitor - Network Tester" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Cyan

# Function to list all network-tester containers
function Show-Containers {
    Write-Host "📋 Listing all network-tester containers..." -ForegroundColor Yellow
    az container list `
        --resource-group $ResourceGroup `
        --query "[?contains(name, 'network-tester')].{Name:name, Status:provisioningState, RestartPolicy:restartPolicy, CreatedTime:creationTime}" `
        --output table
}

# Function to get detailed status of a specific container
function Get-ContainerStatus {
    param([string]$ContainerName)
    
    Write-Host "🔍 Detailed status for container: $ContainerName" -ForegroundColor Yellow
    Write-Host ("-" * 40) -ForegroundColor Gray
    
    az container show `
        --resource-group $ResourceGroup `
        --name $ContainerName `
        --query "{Name:name, Status:provisioningState, State:containers[0].instanceView.currentState.state, StartTime:containers[0].instanceView.currentState.startTime, FinishTime:containers[0].instanceView.currentState.finishTime, ExitCode:containers[0].instanceView.currentState.exitCode, RestartCount:containers[0].instanceView.restartCount, CPU:containers[0].resources.requests.cpu, Memory:containers[0].resources.requests.memoryInGB}" `
        --output table
}

# Function to get container logs
function Get-ContainerLogs {
    param([string]$ContainerName)
    
    Write-Host "📋 Logs for container: $ContainerName" -ForegroundColor Yellow
    Write-Host ("-" * 40) -ForegroundColor Gray
    
    az container logs `
        --resource-group $ResourceGroup `
        --name $ContainerName
}

# Function to find the latest network-tester container
function Find-LatestContainer {
    $result = az container list `
        --resource-group $ResourceGroup `
        --query "[?contains(name, 'network-tester')] | sort_by(@, &creationTime) | [-1].name" `
        --output tsv
    return $result
}

# Function for complete report
function Get-CompleteReport {
    param([string]$ContainerName)
    
    Write-Host "🎯 Complete Report for: $ContainerName" -ForegroundColor Green
    Write-Host ("=" * 60) -ForegroundColor Green
    
    Get-ContainerStatus -ContainerName $ContainerName
    Write-Host ""
    Get-ContainerLogs -ContainerName $ContainerName
}

# Main menu
function Show-Menu {
    Write-Host ""
    Write-Host "🎯 What would you like to do?" -ForegroundColor Cyan
    Write-Host "1. List all network-tester containers"
    Write-Host "2. Get status of latest container"
    Write-Host "3. Get logs of latest container"
    Write-Host "4. Complete report of latest container"
    Write-Host "5. Get status of specific container"
    Write-Host "6. Get logs of specific container"
    Write-Host "0. Exit"
    Write-Host ""
}

# Main execution based on parameter or interactive menu
switch ($Action.ToLower()) {
    "list" {
        Show-Containers
        break
    }
    "latest-status" {
        $LatestContainer = Find-LatestContainer
        if ($LatestContainer) {
            Get-ContainerStatus -ContainerName $LatestContainer
        } else {
            Write-Host "❌ No network-tester containers found" -ForegroundColor Red
        }
        break
    }
    "latest-logs" {
        $LatestContainer = Find-LatestContainer
        if ($LatestContainer) {
            Get-ContainerLogs -ContainerName $LatestContainer
        } else {
            Write-Host "❌ No network-tester containers found" -ForegroundColor Red
        }
        break
    }
    "latest-report" {
        $LatestContainer = Find-LatestContainer
        if ($LatestContainer) {
            Get-CompleteReport -ContainerName $LatestContainer
        } else {
            Write-Host "❌ No network-tester containers found" -ForegroundColor Red
        }
        break
    }
    "status" {
        if ($ContainerName) {
            Get-ContainerStatus -ContainerName $ContainerName
        } else {
            Write-Host "❌ Container name required for this action" -ForegroundColor Red
        }
        break
    }
    "logs" {
        if ($ContainerName) {
            Get-ContainerLogs -ContainerName $ContainerName
        } else {
            Write-Host "❌ Container name required for this action" -ForegroundColor Red
        }
        break
    }
    default {
        # Interactive menu
        do {
            Show-Menu
            $choice = Read-Host "Choose an option (0-6)"
            
            switch ($choice) {
                "1" {
                    Show-Containers
                }
                "2" {
                    $LatestContainer = Find-LatestContainer
                    if ($LatestContainer) {
                        Get-ContainerStatus -ContainerName $LatestContainer
                    } else {
                        Write-Host "❌ No network-tester containers found" -ForegroundColor Red
                    }
                }
                "3" {
                    $LatestContainer = Find-LatestContainer
                    if ($LatestContainer) {
                        Get-ContainerLogs -ContainerName $LatestContainer
                    } else {
                        Write-Host "❌ No network-tester containers found" -ForegroundColor Red
                    }
                }
                "4" {
                    $LatestContainer = Find-LatestContainer
                    if ($LatestContainer) {
                        Get-CompleteReport -ContainerName $LatestContainer
                    } else {
                        Write-Host "❌ No network-tester containers found" -ForegroundColor Red
                    }
                }
                "5" {
                    Show-Containers
                    Write-Host ""
                    $containerName = Read-Host "Enter container name"
                    if ($containerName) {
                        Get-ContainerStatus -ContainerName $containerName
                    }
                }
                "6" {
                    Show-Containers
                    Write-Host ""
                    $containerName = Read-Host "Enter container name"
                    if ($containerName) {
                        Get-ContainerLogs -ContainerName $containerName
                    }
                }
                "0" {
                    Write-Host "👋 Goodbye!" -ForegroundColor Green
                    return
                }
                default {
                    Write-Host "❌ Invalid option. Please choose 0-6." -ForegroundColor Red
                }
            }
            
            if ($choice -ne "0") {
                Write-Host ""
                Read-Host "Press Enter to continue"
            }
        } while ($choice -ne "0")
    }
} 