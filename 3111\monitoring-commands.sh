#!/bin/bash

# Configuration
RESOURCE_GROUP="RG-EDGMON-RUN"
JOB_NAME="eon-monitoring-job"
STORAGE_ACCOUNT="scriptsmonitoring2025"

echo "🔍 Monitoring Commands for EON Monitoring Job"
echo "=============================================="

# Interactive menu
while true; do
    echo ""
    echo "Choose an option:"
    echo "1. View job status"
    echo "2. List recent executions"
    echo "3. View logs from latest execution"
    echo "4. Start job manually"
    echo "5. View result files"
    echo "6. Download CSV logs"
    echo "7. Download screenshots"
    echo "8. Modify schedule"
    echo "9. View performance metrics"
    echo "0. Exit"
    echo ""
    read -p "Your choice (0-9): " choice

    case $choice in
        1)
            echo "📊 Job status:"
            az containerapp job show --name $JOB_NAME --resource-group $RESOURCE_GROUP --query "{Name:name,State:properties.configuration.triggerType,Schedule:properties.configuration.scheduleTriggerConfig.cronExpression,LastExecution:properties.latestRevisionName}" --output table
            ;;
        2)
            echo "📋 Recent executions:"
            az containerapp job execution list --name $JOB_NAME --resource-group $RESOURCE_GROUP --query "[?properties.status=='Succeeded' || properties.status=='Failed' || properties.status=='Running'].{Name:name,Status:properties.status,StartTime:properties.startTime,EndTime:properties.endTime}" --output table
            ;;
        3)
            echo "📝 Logs from latest execution:"
            LATEST_EXECUTION=$(az containerapp job execution list --name $JOB_NAME --resource-group $RESOURCE_GROUP --query "[0].name" --output tsv)
            if [ ! -z "$LATEST_EXECUTION" ]; then
                az containerapp job logs show --name $JOB_NAME --resource-group $RESOURCE_GROUP --execution-name $LATEST_EXECUTION
            else
                echo "❌ No execution found"
            fi
            ;;
        4)
            echo "🚀 Starting job manually..."
            az containerapp job start --name $JOB_NAME --resource-group $RESOURCE_GROUP
            echo "✅ Job started"
            ;;
        5)
            echo "📁 Files in Azure Files:"
            az storage file list --account-name $STORAGE_ACCOUNT --share-name "script-monitoring" --path "3111" --output table
            ;;
        6)
            echo "💾 Downloading CSV results file..."
            mkdir -p ./downloads
            az storage file download --account-name $STORAGE_ACCOUNT --share-name "script-monitoring" --path "3111/3111_RTR.csv" --dest "./downloads/3111_RTR.csv"
            echo "✅ File downloaded to ./downloads/3111_RTR.csv"
            ;;
        7)
            echo "🖼️  Downloading recent screenshots..."
            mkdir -p ./downloads/screenshots
            az storage file list --account-name $STORAGE_ACCOUNT --share-name "script-monitoring" --path "3111/screenshots" --query "[?properties.contentType=='image/png'].name" --output tsv | head -10 | while read filename; do
                if [ ! -z "$filename" ]; then
                    az storage file download --account-name $STORAGE_ACCOUNT --share-name "script-monitoring" --path "3111/screenshots/$filename" --dest "./downloads/screenshots/$filename"
                    echo "📸 Downloaded: $filename"
                fi
            done
            ;;
        8)
            echo "⏰ Schedule modification"
            echo "Current schedule:"
            az containerapp job show --name $JOB_NAME --resource-group $RESOURCE_GROUP --query "properties.configuration.scheduleTriggerConfig.cronExpression" --output tsv
            echo ""
            echo "Schedule examples:"
            echo "  - Every 15 minutes: '0 */15 * * * *'"
            echo "  - Every hour: '0 0 * * * *'"
            echo "  - Daily at 8 AM: '0 0 8 * * *'"
            echo "  - Monday to Friday at 9 AM: '0 0 9 * * 1-5'"
            echo ""
            read -p "New cron schedule (or Enter to cancel): " new_schedule
            if [ ! -z "$new_schedule" ]; then
                az containerapp job update --name $JOB_NAME --resource-group $RESOURCE_GROUP --cron-expression "$new_schedule"
                echo "✅ Schedule updated: $new_schedule"
            fi
            ;;
        9)
            echo "📈 Performance metrics (recent executions):"
            # Download and analyze CSV file
            mkdir -p ./temp
            az storage file download --account-name $STORAGE_ACCOUNT --share-name "script-monitoring" --path "3111/3111_RTR.csv" --dest "./temp/metrics.csv" 2>/dev/null
            if [ -f "./temp/metrics.csv" ]; then
                echo ""
                echo "📊 Summary of recent measurements:"
                tail -10 ./temp/metrics.csv | awk -F';' 'NR>1 {
                    print "Date: " $2 " " $3
                    print "  - Page load: " $4 "ms"
                    print "  - Login: " $5 "ms" 
                    print "  - Meter reading: " $6 "ms"
                    print "  - Installments: " $7 "ms"
                    print "  - Logout: " $8 "ms"
                    print "  - Total: " ($4+$5+$6+$7+$8) "ms"
                    print "---"
                }'
                rm -f ./temp/metrics.csv
            else
                echo "❌ Unable to retrieve metrics"
            fi
            ;;
        0)
            echo "👋 Goodbye!"
            exit 0
            ;;
        *)
            echo "❌ Invalid option, please choose between 0 and 9"
            ;;
    esac
    
    echo ""
    read -p "Press Enter to continue..."
done