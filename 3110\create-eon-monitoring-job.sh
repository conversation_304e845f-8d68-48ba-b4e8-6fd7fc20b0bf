#!/bin/bash

# Configuration
RESOURCE_GROUP="RG-EDGMON-RUN"
KEYVAULT_NAME="scriptsmonitoring2025"
ACR_NAME="scriptmonregistry2025"
STORAGE_ACCOUNT="scriptsmonitoring2025"
MANAGED_IDENTITY="scriptmonregistry2025-identity"
CONTAINER_APP_ENV="managedEnvironment-RGEDGMONRUN-b130"
JOB_NAME="eon-monitoring-3110-job"
IMAGE_NAME="eon-monitoring-3110"
IMAGE_TAG="latest"

echo "🚀 Création du Job Container App pour EON Monitoring"
echo "=================================================="

# Vérifier si connecté à Azure
echo "📋 Vérification de la connexion Azure..."
az account show > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ Veuillez vous connecter à Azure avec 'az login'"
    exit 1
fi

# Récupérer l'ID de l'identité managée
echo "🔍 Récupération de l'ID de l'identité managée..."
IDENTITY_ID=$(az identity show --name $MANAGED_IDENTITY --resource-group $RESOURCE_GROUP --query id -o tsv)
CLIENT_ID=$(az identity show --name $MANAGED_IDENTITY --resource-group $RESOURCE_GROUP --query clientId -o tsv)

# Vérifier si le job existe déjà
echo ""
echo "🔍 Vérification si le job existe déjà..."
JOB_EXISTS=$(az containerapp job show --name $JOB_NAME --resource-group $RESOURCE_GROUP --query name --output tsv 2>/dev/null)

if [ ! -z "$JOB_EXISTS" ]; then
    echo "⚠️  Le job '$JOB_NAME' existe déjà"
    read -p "Voulez-vous le supprimer et le recréer ? (y/N): " recreate_job
    if [[ "$recreate_job" =~ ^[Yy]$ ]]; then
        echo "🗑️  Suppression du job existant..."
        az containerapp job delete --name $JOB_NAME --resource-group $RESOURCE_GROUP --yes
        echo "✅ Job supprimé"
    else
        echo "ℹ️  Mise à jour du job existant..."
        az containerapp job update \
            --name $JOB_NAME \
            --resource-group $RESOURCE_GROUP \
            --image $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG
        
        if [ $? -eq 0 ]; then
            echo "✅ Job mis à jour avec succès"
            exit 0
        else
            echo "❌ Erreur lors de la mise à jour du job"
            exit 1
        fi
    fi
fi

# Créer le Container App Job
echo ""
echo "🔧 Création du Container App Job..."
az containerapp job create \
    --name $JOB_NAME \
    --resource-group $RESOURCE_GROUP \
    --environment $CONTAINER_APP_ENV \
    --trigger-type "Schedule" \
    --cron-expression "*/5 * * * *" \
    --image $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG \
    --user-assigned $IDENTITY_ID \
    --registry-server $ACR_NAME.azurecr.io \
    --registry-identity $IDENTITY_ID \
    --env-vars AZURE_CLIENT_ID=$CLIENT_ID \
    --cpu 1.0 \
    --memory 2.0Gi \
    --parallelism 1 \
    --replica-completion-count 1 \
    --replica-timeout 3600 \
    --replica-retry-limit 2

if [ $? -ne 0 ]; then
    echo "❌ Erreur lors de la création du job"
    exit 1
fi

echo "✅ Job créé avec succès !"

# Ajouter le volume Azure Files
echo ""
echo "📁 Configuration du volume Azure Files..."
az containerapp job update \
    --name $JOB_NAME \
    --resource-group $RESOURCE_GROUP \
    --set properties.template.volumes='[
        {
            "name": "azure-files-volume",
            "storageType": "AzureFile",
            "storageName": "script-monitoring-storage"
        }
    ]' \
    --set properties.template.containers[0].volumeMounts='[
        {
            "volumeName": "azure-files-volume",
            "mountPath": "/mnt/monitoring"
        }
    ]'

if [ $? -eq 0 ]; then
    echo "✅ Volume Azure Files configuré"
else
    echo "⚠️  Erreur lors de la configuration du volume (le job fonctionne quand même)"
fi

echo ""
echo "🎉 CRÉATION TERMINÉE AVEC SUCCÈS !"
echo "================================="