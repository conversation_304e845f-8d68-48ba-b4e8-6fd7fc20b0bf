import os
import time
from datetime import date, datetime
import string
from playwright.sync_api import <PERSON><PERSON>, sync_playwright, expect
from azure.identity import DefaultAzureCredential
from azure.keyvault.secrets import SecretClient
from azure.storage.blob import BlobServiceClient, ContentSettings

# Azure Configuration
KEY_VAULT_NAME = os.environ.get("KEY_VAULT_NAME")
KEY_VAULT_SECRET_NAME = os.environ.get("KEY_VAULT_SECRET_NAME", "PUW-CREDENTIALS")
STORAGE_ACCOUNT_NAME = os.environ.get("STORAGE_ACCOUNT_NAME")
STORAGE_CONTAINER_NAME = os.environ.get("STORAGE_CONTAINER_NAME", "monitoring-results")
BLOB_NAME_PREFIX = os.environ.get("BLOB_NAME_PREFIX", "8810_RTR")

# Function to retrieve credentials from Azure Key Vault
def get_credentials_from_keyvault():
    try:
        # Use DefaultAzureCredential which supports multiple authentication methods
        credential = DefaultAzureCredential()
        
        # Key Vault URL
        vault_url = f"https://{KEY_VAULT_NAME}.vault.azure.net"
        
        # Create a client for Key Vault
        client = SecretClient(vault_url=vault_url, credential=credential)
        
        # Retrieve the secret
        secret = client.get_secret(KEY_VAULT_SECRET_NAME)
        
        # The secret should be in the format "USERNAME:PASSWORD"
        return secret.value
    except Exception as e:
        print(f"Error retrieving credentials from Azure Key Vault: {e}")
        return None

# Function to log results to Azure Blob Storage
def log_to_blob_storage(log_line):
    try:
        if not STORAGE_ACCOUNT_NAME:
            # If not configured, use fallback local logging
            log_locally(log_line)
            return
            
        # Connect to storage account
        credential = DefaultAzureCredential()
        blob_service_client = BlobServiceClient(
            account_url=f"https://{STORAGE_ACCOUNT_NAME}.blob.core.windows.net",
            credential=credential
        )
        
        # Get container client
        container_client = blob_service_client.get_container_client(STORAGE_CONTAINER_NAME)
        
        # Check if container exists, create it if not
        if not container_client.exists():
            container_client.create_container()
        
        # Blob name (CSV file in storage)
        today = date.today().strftime("%Y%m%d")
        blob_name = f"{BLOB_NAME_PREFIX}_{today}.csv"
        
        # Get blob client
        blob_client = container_client.get_blob_client(blob_name)
        
        # Check if blob exists
        if not blob_client.exists():
            # Create CSV header
            header = "SCRIPT_ID;SCRIPT_DATE;SCRIPT_START_TIME;LOAD_LOGIN_PAGE;LOGIN;CHECK_STATUS;LOGOUT;\n"
            blob_client.upload_blob(header, overwrite=True, content_settings=ContentSettings(content_type="text/csv"))
        
        # Prepare log line
        full_log_line = "8810;" + date.today().strftime("%d.%m.%Y") + ";" + \
            datetime.now().strftime("%H:%M:%S") + ";" + log_line + "\n"
        
        # Append line to existing blob
        blob_client.append_block(full_log_line)
        
    except Exception as e:
        print(f"Error logging to Azure Blob Storage: {e}")
        # Fallback to local logging
        log_locally(log_line)

# Fallback function to log locally
def log_locally(sLine):
    path = "/app/logs"
    isExist = os.path.exists(path)
    if not isExist:
        os.makedirs(path)

    log_file = f"{path}/8810_RTR.csv"
    isExistFile = os.path.exists(log_file)
    
    if not isExistFile:
        with open(log_file, 'a') as datei:
            sHeader = "SCRIPT_ID;SCRIPT_DATE;SCRIPT_START_TIME;LOAD_LOGIN_PAGE;LOGIN;CHECK_STATUS;LOGOUT;\n"
            datei.write(sHeader)

    with open(log_file, 'a') as datei:
        full_line = "8810;" + date.today().strftime("%d.%m.%Y") + ";" + \
            datetime.now().strftime("%H:%M:%S") + ";" + sLine + "\n"
        datei.write(full_line)

# Function to measure time in milliseconds
def GetMS(fTime):
    m1 = int(round(fTime * 1000, 0))
    sm1 = str(m1)
    return sm1

# Function to take a screenshot and save it to Azure Blob Storage
def save_screenshot(page, error_type):
    try:
        if not STORAGE_ACCOUNT_NAME:
            # Fallback to local storage
            screenshots_dir = "/app/screenshots"
            if not os.path.exists(screenshots_dir):
                os.makedirs(screenshots_dir)
                
            timestamp = f"{datetime.now().strftime('%Y%m%d-%H%M%S')}"
            screenshot_path = f"{screenshots_dir}/8810-{timestamp}-{error_type}.png"
            page.screenshot(path=screenshot_path)
            return
            
        # Connect to storage account
        credential = DefaultAzureCredential()
        blob_service_client = BlobServiceClient(
            account_url=f"https://{STORAGE_ACCOUNT_NAME}.blob.core.windows.net",
            credential=credential
        )
        
        # Get container client
        screenshots_container = "monitoring-screenshots"
        container_client = blob_service_client.get_container_client(screenshots_container)
        
        # Check if container exists, create if not
        if not container_client.exists():
            container_client.create_container()
        
        # Capture screenshot in memory
        screenshot_bytes = page.screenshot()
        
        # Blob name
        timestamp = f"{datetime.now().strftime('%Y%m%d-%H%M%S')}"
        blob_name = f"8810-{timestamp}-{error_type}.png"
        
        # Get blob client
        blob_client = container_client.get_blob_client(blob_name)
        
        # Upload screenshot
        blob_client.upload_blob(
            screenshot_bytes, 
            overwrite=True,
            content_settings=ContentSettings(content_type="image/png")
        )
        
    except Exception as e:
        print(f"Error saving screenshot: {e}")
        # If error, try to save locally
        try:
            screenshots_dir = "/app/screenshots"
            if not os.path.exists(screenshots_dir):
                os.makedirs(screenshots_dir)
                
            timestamp = f"{datetime.now().strftime('%Y%m%d-%H%M%S')}"
            screenshot_path = f"{screenshots_dir}/8810-{timestamp}-{error_type}.png"
            page.screenshot(path=screenshot_path)
        except:
            pass

def run(playwright: Playwright) -> None:
    # Retrieve credentials from Azure Key Vault
    creds = get_credentials_from_keyvault()
    
    if not creds:
        print("Unable to retrieve credentials. Stopping script.")
        return
    
    uid, pwd = creds.split(':')

    # Configure browser with Playwright
    browser = playwright.chromium.launch(headless=True)  # headless=True for execution without interface
    context = browser.new_context()
    page = context.new_page()

    bContinue = True

    ############################################################################
    # Load Login Page
    ############################################################################

    start = time.time()

    try:
        try:
            page.goto(
                "https://puw.apps.eon.com/sap/bc/gui/sap/its/webgui?sap-client=100&sap-language=DE")
        except:
            page.goto(
                "https://puw.apps.eon.com/sap/bc/gui/sap/its/webgui?sap-client=100&sap-language=DE")
        
        page.get_by_label("Benutzer", exact=True).click()
        page.get_by_label("Benutzer", exact=True).fill(uid)
        page.get_by_label("Kennwort", exact=True).click()
        page.get_by_label("Kennwort", exact=True).fill(pwd)
        stop = time.time()
    except Exception as e:
        print(f"Error loading login page: {e}")
        stop = 0
        start = 2
        bContinue = False
        save_screenshot(page, "LOAD_LOGIN_PAGE")

    sm1 = GetMS(stop-start)

    ############################################################################
    # Login
    ############################################################################

    start = time.time()
    if bContinue:
        try:
            page.get_by_label("Kennwort", exact=True).press("Enter")
            page.wait_for_url(
                "https://puw.apps.eon.com/sap/bc/gui/sap/its/webgui?sap-client=100&sap-language=DE")

            stop = time.time()
        except Exception as e:
            print(f"Error during login: {e}")
            stop = 0
            start = 2
            bContinue = False
            save_screenshot(page, "LOGIN")                
    else:
        stop = 0
        start = 2

    sm2 = GetMS(stop-start)

    ############################################################################
    # CHECK STATUS
    ############################################################################

    start = time.time()
    if bContinue:
        try:
            page.get_by_text(
                "SAP Easy Access - Benutzermenü für MONHPQC1").click()
            stop = time.time()
        except Exception as e:
            print(f"Error checking status: {e}")
            stop = 0
            start = 2
            bContinue = False
            save_screenshot(page, "CHECK_STATUS")
    else:
        stop = 0
        start = 2

    sm3 = GetMS(stop-start)

    ############################################################################
    # Logout
    ############################################################################

    start = time.time()
    try:
        page.get_by_role("button", name="Abmelden").click()
        page.get_by_role("button", name="Ja").click()
        page.locator(".lowerCenter").click()
        stop = time.time()
    except Exception as e:
        print(f"Error during logout: {e}")
        stop = 0
        start = 2
        bContinue = False
        save_screenshot(page, "LOGOUT")

    sm4 = GetMS(stop-start)

    # Save results
    log_to_blob_storage(f"{sm1};{sm2};{sm3};{sm4};")

    # Close browser
    context.close()
    browser.close()

# Main entry point
if __name__ == "__main__":
    with sync_playwright() as playwright:
        run(playwright)
