#!/bin/bash

# Enterprise SAP Playwright Tester Deployment Script
# Based on 8810 architecture with KeyVault, Blob Storage, and Splunk
# Runs every 5 minutes via Azure Container Apps

set -e

# ==============================================
# ENTERPRISE CONFIGURATION
# ==============================================

RESOURCE_GROUP="RG-EDGMON-RUN"
ACR_NAME="scriptmonregistry2025"
ACR_LOGIN_SERVER="scriptmonregistry2025.azurecr.io"
ACR_USERNAME="SCRIPTMONREGISTRY2025"
ACR_PASSWORD="****************************************************"

# Enterprise Container Configuration
CONTAINER_APP_NAME="sap-playwright-enterprise-10001"
IMAGE_NAME="sap-playwright-enterprise:latest"

# ARM Template Files
ARM_TEMPLATE="template-enterprise.json"
ARM_PARAMETERS="parameters-enterprise.json"

echo "🏢 Starting Enterprise SAP Playwright Deployment"
echo "================================================="
echo "🎯 Container App: $CONTAINER_APP_NAME"
echo "🔐 With KeyVault integration"
echo "📦 With Blob Storage integration"  
echo "📊 With Splunk integration"
echo "⏰ Scheduled every 5 minutes"
echo "================================================="

# Build enterprise container
echo "🔨 Building enterprise Docker image..."
docker build -f Dockerfile-enterprise -t $IMAGE_NAME .

echo "📤 Tagging for ACR..."
docker tag $IMAGE_NAME $ACR_LOGIN_SERVER/$IMAGE_NAME

echo "🔐 Logging into ACR..."
echo $ACR_PASSWORD | docker login $ACR_LOGIN_SERVER -u $ACR_USERNAME --password-stdin

echo "📤 Pushing enterprise image to ACR..."
docker push $ACR_LOGIN_SERVER/$IMAGE_NAME

# Deploy using ARM template
echo "🚀 Deploying enterprise infrastructure with ARM template..."

if [ ! -f "$ARM_TEMPLATE" ] || [ ! -f "$ARM_PARAMETERS" ]; then
    echo "❌ ARM template files not found!"
    echo "   Required: $ARM_TEMPLATE, $ARM_PARAMETERS"
    exit 1
fi

echo "📋 ARM Template: $ARM_TEMPLATE"
echo "📋 Parameters: $ARM_PARAMETERS"

# Deploy the ARM template
DEPLOYMENT_NAME="sap-playwright-enterprise-$(date +%s)"

az deployment group create \
    --resource-group $RESOURCE_GROUP \
    --name $DEPLOYMENT_NAME \
    --template-file $ARM_TEMPLATE \
    --parameters @$ARM_PARAMETERS \
    --verbose

echo "✅ Enterprise deployment completed!"
echo ""
echo "🏢 ENTERPRISE SAP PLAYWRIGHT TESTER DEPLOYED"
echo "============================================="
echo "📱 Container App: $CONTAINER_APP_NAME"
echo "⏰ Schedule: Every 5 minutes"
echo "🔐 Credentials: Azure KeyVault"
echo "📦 Storage: Azure Blob Storage"
echo "📊 Logging: Splunk + Azure Log Analytics"
echo ""
echo "📋 Next Steps:"
echo "1. Configure SAP credentials in KeyVault:"
echo "   az keyvault secret set --vault-name YOUR_VAULT --name SAP-LAUNCHPAD-CONFIG --value '{\"url\":\"...\",\"username\":\"...\",\"password\":\"...\",\"expected_keywords\":[\"launchpad\",\"sap\",\"fiori\"]}'"
echo ""
echo "2. Monitor execution:"
echo "   az containerapp logs show --name $CONTAINER_APP_NAME --resource-group $RESOURCE_GROUP --follow"
echo ""
echo "3. Check Blob Storage for results:"
echo "   Container: sap-monitoring-results"
echo "   Screenshots: screenshots/"
echo "   Results: results/"
echo "   Logs: logs/"
echo ""
echo "4. Configure Splunk HEC if needed:"
echo "   Update parameters-enterprise.json with your Splunk URL and token"
echo ""
echo "🎉 Enterprise SAP Playwright monitoring is now running every 5 minutes!" 