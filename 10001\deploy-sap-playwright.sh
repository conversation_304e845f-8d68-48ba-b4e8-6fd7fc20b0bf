#!/bin/bash

# Azure Container Deployment Script - SAP Playwright Tester
# Project 10001 - Independent SAP Launchpad Testing

set -e

# ==============================================
# CONFIGURATION VARIABLES - MODIFY HERE
# ==============================================

RESOURCE_GROUP="RG-EDGMON-RUN"
ACR_NAME="scriptmonregistry2025"
ACR_LOGIN_SERVER="scriptmonregistry2025.azurecr.io"
ACR_USERNAME="SCRIPTMONREGISTRY2025"
ACR_PASSWORD="****************************************************"
CONTAINER_NAME="sap-playwright-tester-$(date +%s)"
IMAGE_NAME="sap-playwright-tester:latest"

# Test Configuration
SAP_URL="https://oneb-central-dev.launchpad.cfapps.eu10.hana.ondemand.com/site?siteId=1af3caee-8117-49e3-b787-7ad74914b5cb"
TIMEOUT="60000"
TAKE_SCREENSHOT="true"
BROWSER_TYPE="chromium"

# ==============================================
# DEPLOYMENT LOGIC
# ==============================================

echo "🚀 Starting SAP Playwright Tester deployment..."
echo "📋 Project: 10001 - SAP Launchpad Testing"
echo "🎯 Target URL: $SAP_URL"
echo "Container: $CONTAINER_NAME"
echo "="*80

# Build the Docker image
echo "🔨 Building Docker image..."
docker build -t $IMAGE_NAME .

echo "📤 Tagging image for ACR..."
docker tag $IMAGE_NAME $ACR_LOGIN_SERVER/$IMAGE_NAME

echo "🔐 Logging into ACR..."
echo $ACR_PASSWORD | docker login $ACR_LOGIN_SERVER -u $ACR_USERNAME --password-stdin

echo "📤 Pushing image to ACR..."
docker push $ACR_LOGIN_SERVER/$IMAGE_NAME

# Create Container Instance with enhanced logging
echo "🚀 Creating Azure Container Instance..."
az container create \
    --resource-group $RESOURCE_GROUP \
    --name $CONTAINER_NAME \
    --image $ACR_LOGIN_SERVER/$IMAGE_NAME \
    --cpu 2 \
    --memory 4 \
    --os-type Linux \
    --restart-policy Never \
    --registry-login-server $ACR_LOGIN_SERVER \
    --registry-username $ACR_USERNAME \
    --registry-password "$ACR_PASSWORD" \
    --environment-variables \
        TARGET_URL="$SAP_URL" \
        PAGE_TIMEOUT="$TIMEOUT" \
        TAKE_SCREENSHOT="$TAKE_SCREENSHOT" \
        BROWSER_TYPE="$BROWSER_TYPE" \
        OUTPUT_FILE="/app/sap_playwright_test_results.json" \
        SCREENSHOT_FILE="/app/sap_screenshot.png" \
        EXPECTED_TITLE_KEYWORDS="launchpad,sap,fiori"

echo "✅ Container deployed successfully!"
echo "📋 Container name: $CONTAINER_NAME"
echo "🎯 Testing URL: $SAP_URL"

# Wait for container to start
echo "⏳ Waiting for container to initialize..."
sleep 30

echo ""
echo "🎉 SAP Playwright Tester deployment complete!"
echo ""
echo "📋 Next Steps:"
echo "1. Monitor the container: ./monitor-sap-playwright.sh"
echo "2. Get full results: ./monitor-sap-playwright.sh latest-summary"
echo "3. View logs: ./monitor-sap-playwright.sh latest-logs"
echo "4. Real-time monitoring: ./monitor-sap-playwright.sh monitor"
echo ""
echo "The container will test the SAP Launchpad with enhanced logging:"
echo "📄 • Detailed progress tracking for each step"
echo "📸 • Full-page screenshot capture"
echo "🔍 • Comprehensive error reporting"
echo "📊 • Performance metrics collection" 