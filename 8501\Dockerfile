FROM mcr.microsoft.com/playwright/python:v1.41.1-jammy

WORKDIR /app

# Installer tzdata et configurer le fuseau horaire de manière non-interactive
ENV DEBIAN_FRONTEND=noninteractive
RUN apt-get update && apt-get install -y tzdata
# Définir le fuseau horaire sur Europe/Berlin (Allemagne)
ENV TZ=Europe/Berlin
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Copy requirements file
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the monitoring script
COPY mon_powercloud_getContractsByCustomerId_NG.py .

# Create necessary directories
RUN mkdir -p /tmp/monitoring/results

# Set environment variables
ENV PYTHONUNBUFFERED=1

# Default command
CMD ["python", "-u", "mon_powercloud_getContractsByCustomerId_NG.py"]


