#!/usr/bin/env python3

"""
SAP Playwright Connectivity Tester
Tests SAP Launchpad connectivity using Playwright
Enhanced with detailed logging and progress tracking
"""

import asyncio
import json
import os
import traceback
from datetime import datetime, timezone
from playwright.async_api import async_playwright
from urllib.parse import urlparse

# ==============================================
# CONFIGURATION VARIABLES - MODIFY HERE OR SET AS ENVIRONMENT VARIABLES
# ==============================================

# Target URL to test (can be overridden with TARGET_URL env var)
TARGET_URL = os.getenv("TARGET_URL", "https://oneb-central-dev.launchpad.cfapps.eu10.hana.ondemand.com/site?siteId=1af3caee-8117-49e3-b787-7ad74914b5cb")

# Timeout settings (in milliseconds)
PAGE_TIMEOUT = int(os.getenv("PAGE_TIMEOUT", "60000"))  # 60 seconds
NAVIGATION_TIMEOUT = int(os.getenv("NAVIGATION_TIMEOUT", "30000"))  # 30 seconds
WAIT_TIMEOUT = int(os.getenv("WAIT_TIMEOUT", "10000"))  # 10 seconds

# Output configuration
OUTPUT_FILE = os.getenv("OUTPUT_FILE", "/app/sap_playwright_test_results.json")
SCREENSHOT_FILE = os.getenv("SCREENSHOT_FILE", "/app/sap_screenshot.png")
TAKE_SCREENSHOT = os.getenv("TAKE_SCREENSHOT", "true").lower() in ("true", "1", "yes")

# Browser settings
BROWSER_TYPE = os.getenv("BROWSER_TYPE", "chromium")  # Options: chromium, firefox, webkit
HEADLESS = os.getenv("HEADLESS", "true").lower() in ("true", "1", "yes")
VIEWPORT_WIDTH = int(os.getenv("VIEWPORT_WIDTH", "1920"))
VIEWPORT_HEIGHT = int(os.getenv("VIEWPORT_HEIGHT", "1080"))

# Test parameters
USER_AGENT = os.getenv("USER_AGENT", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
EXPECTED_TITLE_KEYWORDS = os.getenv("EXPECTED_TITLE_KEYWORDS", "launchpad,sap,fiori").split(",")
WAIT_FOR_SELECTOR = os.getenv("WAIT_FOR_SELECTOR") or None  # Optional: CSS selector to wait for

# ==============================================
# MAIN TESTING LOGIC
# ==============================================

class SAPPlaywrightTester:
    def __init__(self):
        self.results = {
            "test_info": {
                "url": TARGET_URL,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "browser": BROWSER_TYPE,
                "user_agent": USER_AGENT,
                "configuration": {
                    "page_timeout": PAGE_TIMEOUT,
                    "navigation_timeout": NAVIGATION_TIMEOUT,
                    "headless": HEADLESS,
                    "viewport": f"{VIEWPORT_WIDTH}x{VIEWPORT_HEIGHT}",
                    "screenshot_enabled": TAKE_SCREENSHOT
                }
            },
            "connectivity": {},
            "page_info": {},
            "performance": {},
            "errors": [],
            "success": False,
            "progress": []
        }
    
    def log_progress(self, step, message, success=True):
        """Log progress step with timestamp"""
        timestamp = datetime.now(timezone.utc).isoformat()
        status = "✅" if success else "❌"
        print(f"{status} {step}: {message}")
        
        self.results["progress"].append({
            "step": step,
            "message": message,
            "success": success,
            "timestamp": timestamp
        })
    
    async def run_test(self):
        """Main test execution with detailed progress tracking"""
        print("="*80)
        print("🚀 SAP PLAYWRIGHT CONNECTIVITY TESTER")
        print("="*80)
        print(f"🎯 Target URL: {TARGET_URL}")
        print(f"🕒 Test started at: {datetime.now().isoformat()}")
        print("")
        print("⚙️  Configuration:")
        print(f"   • Browser: {BROWSER_TYPE}")
        print(f"   • Headless: {HEADLESS}")
        print(f"   • Viewport: {VIEWPORT_WIDTH}x{VIEWPORT_HEIGHT}")
        print(f"   • Page Timeout: {PAGE_TIMEOUT}ms")
        print(f"   • Navigation Timeout: {NAVIGATION_TIMEOUT}ms")
        print(f"   • Screenshot: {TAKE_SCREENSHOT}")
        print(f"   • Expected keywords: {EXPECTED_TITLE_KEYWORDS}")
        print("-" * 80)
        
        start_time = datetime.now()
        
        try:
            self.log_progress("1/6", "Initializing Playwright environment")
            async with async_playwright() as p:
                self.log_progress("1/6", "Playwright initialized successfully")
                
                # Launch browser
                self.log_progress("2/6", f"Launching {BROWSER_TYPE} browser...")
                browser_start = datetime.now()
                browser = await self._launch_browser(p)
                browser_launch_time = (datetime.now() - browser_start).total_seconds()
                self.log_progress("2/6", f"Browser launched in {browser_launch_time:.2f}s")
                
                # Create page
                self.log_progress("3/6", "Creating and configuring page...")
                page = await browser.new_page()
                await self._configure_page(page)
                self.log_progress("3/6", "Page created and configured successfully")
                
                # Navigate to URL
                self.log_progress("4/6", f"Navigating to target URL...")
                navigation_start = datetime.now()
                response = await self._navigate_to_url(page)
                navigation_time = (datetime.now() - navigation_start).total_seconds()
                self.log_progress("4/6", f"Navigation completed in {navigation_time:.2f}s (Status: {response.status})")
                
                # Analyze page
                self.log_progress("5/6", "Analyzing page content and performance...")
                await self._analyze_page(page, response)
                self.log_progress("5/6", "Page analysis completed successfully")
                
                # Take screenshot if enabled
                if TAKE_SCREENSHOT:
                    self.log_progress("6/6", "Taking screenshot of the page...")
                    await self._take_screenshot(page)
                    self.log_progress("6/6", f"Screenshot saved to {SCREENSHOT_FILE}")
                else:
                    self.log_progress("6/6", "Screenshot disabled - skipping")
                
                # Record performance metrics
                total_time = (datetime.now() - start_time).total_seconds()
                self.results["performance"] = {
                    "browser_launch_time": browser_launch_time,
                    "navigation_time": navigation_time,
                    "total_test_time": total_time
                }
                
                print(f"\n🔧 Closing browser...")
                await browser.close()
                print("✅ Browser closed successfully")
                
                self.results["success"] = True
                self.log_progress("COMPLETE", f"Test completed successfully in {total_time:.2f}s")
                
        except Exception as e:
            error_msg = f"Test failed: {str(e)}"
            print(f"\n❌ {error_msg}")
            print(f"🔍 Error type: {type(e).__name__}")
            traceback_str = traceback.format_exc()
            print(f"📋 Full traceback:")
            print(traceback_str)
            
            self.log_progress("ERROR", error_msg, success=False)
            
            self.results["errors"].append({
                "type": "test_execution_error",
                "message": error_msg,
                "error_type": type(e).__name__,
                "traceback": traceback_str,
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
            self.results["success"] = False
    
    async def _launch_browser(self, playwright):
        """Launch browser with detailed configuration logging"""
        print(f"   🌐 Browser type: {BROWSER_TYPE}")
        print(f"   🎭 Headless mode: {HEADLESS}")
        
        try:
            browser_options = {
                "headless": HEADLESS,
            }
            
            print(f"   ⚡ Initializing {BROWSER_TYPE} engine...")
            if BROWSER_TYPE == "chromium":
                browser = await playwright.chromium.launch(**browser_options)
            elif BROWSER_TYPE == "firefox":
                browser = await playwright.firefox.launch(**browser_options)
            elif BROWSER_TYPE == "webkit":
                browser = await playwright.webkit.launch(**browser_options)
            else:
                raise ValueError(f"Unsupported browser type: {BROWSER_TYPE}")
            
            print(f"   ✅ Browser engine started successfully")
            return browser
            
        except Exception as e:
            print(f"   ❌ Failed to launch browser: {str(e)}")
            raise
    
    async def _configure_page(self, page):
        """Configure page settings with detailed logging"""
        print(f"   📐 Setting viewport: {VIEWPORT_WIDTH}x{VIEWPORT_HEIGHT}")
        await page.set_viewport_size(width=VIEWPORT_WIDTH, height=VIEWPORT_HEIGHT)
        
        print(f"   🌐 Setting User-Agent: {USER_AGENT[:50]}...")
        await page.set_extra_http_headers({"User-Agent": USER_AGENT})
        
        print(f"   ⏱️  Setting timeouts - Page: {PAGE_TIMEOUT}ms, Navigation: {NAVIGATION_TIMEOUT}ms")
        page.set_default_timeout(PAGE_TIMEOUT)
        page.set_default_navigation_timeout(NAVIGATION_TIMEOUT)
        
        print(f"   ✅ Page configuration completed")
    
    async def _navigate_to_url(self, page):
        """Navigate to target URL with detailed progress tracking"""
        print(f"   🔗 Target: {TARGET_URL}")
        print(f"   ⏳ Wait condition: domcontentloaded")
        print(f"   ⏱️  Timeout: {NAVIGATION_TIMEOUT}ms")
        
        try:
            print(f"   🚀 Starting navigation request...")
            response = await page.goto(TARGET_URL, wait_until="domcontentloaded")
            
            print(f"   📡 HTTP {response.status} {response.status_text}")
            print(f"   🏁 Final URL: {response.url}")
            print(f"   ✅ Response OK: {response.ok}")
            
            # Check for redirects
            if response.url != TARGET_URL:
                print(f"   🔄 Redirect detected: {TARGET_URL} → {response.url}")
            
            self.results["connectivity"] = {
                "status_code": response.status,
                "status_text": response.status_text,
                "url": response.url,
                "original_url": TARGET_URL,
                "redirected": response.url != TARGET_URL,
                "headers": dict(response.headers),
                "ok": response.ok
            }
            
            return response
            
        except Exception as e:
            print(f"   ❌ Navigation failed: {str(e)}")
            print(f"   🔍 Error type: {type(e).__name__}")
            raise
    
    async def _analyze_page(self, page, response):
        """Analyze loaded page with comprehensive logging"""
        try:
            # Wait for optional selector if specified
            if WAIT_FOR_SELECTOR:
                print(f"   🎯 Waiting for selector: {WAIT_FOR_SELECTOR}")
                await page.wait_for_selector(WAIT_FOR_SELECTOR, timeout=WAIT_TIMEOUT)
                print(f"   ✅ Selector found and loaded")
            else:
                print(f"   ℹ️  No specific selector to wait for")
            
            # Get page information
            print(f"   📄 Extracting page information...")
            title = await page.title()
            url = page.url
            print(f"   📋 Title: '{title}'")
            print(f"   🌐 Final URL: {url}")
            
            # Check for JavaScript errors
            print(f"   🔍 Setting up JavaScript error monitoring...")
            js_errors = []
            
            def handle_js_error(error):
                error_msg = str(error)
                js_errors.append(error_msg)
                print(f"   ⚠️  JavaScript Error: {error_msg}")
            
            page.on("pageerror", handle_js_error)
            
            # Wait for page to fully load
            print(f"   ⏳ Waiting 3 seconds for complete page load...")
            await asyncio.sleep(3)
            
            # Get page metrics
            print(f"   📊 Collecting browser performance metrics...")
            try:
                metrics = await page.evaluate("() => JSON.stringify(performance.getEntriesByType('navigation')[0])")
                performance_data = json.loads(metrics) if metrics else {}
                print(f"   ✅ Performance data collected: {len(performance_data)} metrics")
                
                # Extract key performance metrics
                if performance_data:
                    load_time = performance_data.get('loadEventEnd', 0) - performance_data.get('navigationStart', 0)
                    dom_ready = performance_data.get('domContentLoadedEventEnd', 0) - performance_data.get('navigationStart', 0)
                    print(f"   ⚡ Page load time: {load_time:.0f}ms")
                    print(f"   ⚡ DOM ready time: {dom_ready:.0f}ms")
                    
            except Exception as perf_error:
                print(f"   ⚠️  Performance data collection failed: {str(perf_error)}")
                performance_data = {}
            
            # Check title keywords
            title_matches = [keyword.strip() for keyword in EXPECTED_TITLE_KEYWORDS if keyword.strip().lower() in title.lower()]
            print(f"   🔍 Title keyword analysis:")
            print(f"      Expected: {EXPECTED_TITLE_KEYWORDS}")
            print(f"      Matched: {title_matches}")
            
            # Get page content info
            try:
                content_length = await page.evaluate("() => document.documentElement.outerHTML.length")
                print(f"   📏 Page content size: {content_length:,} characters")
            except:
                content_length = 0
            
            self.results["page_info"] = {
                "title": title,
                "final_url": url,
                "title_contains_expected": len(title_matches) > 0,
                "title_matched_keywords": title_matches,
                "page_loaded": True,
                "content_length": content_length,
                "javascript_errors": js_errors,
                "javascript_error_count": len(js_errors),
                "performance_metrics": performance_data
            }
            
            print(f"   📈 Analysis summary:")
            print(f"      • Page loaded: ✅")
            print(f"      • Title matches keywords: {'✅' if title_matches else '❌'}")
            print(f"      • JavaScript errors: {len(js_errors)}")
            print(f"      • Content size: {content_length:,} chars")
            
        except Exception as e:
            error_msg = f"Page analysis failed: {str(e)}"
            print(f"   ❌ {error_msg}")
            traceback_str = traceback.format_exc()
            print(f"   📋 Analysis traceback:")
            for line in traceback_str.split('\n'):
                if line.strip():
                    print(f"      {line}")
            
            self.results["errors"].append({
                "type": "page_analysis_error",
                "message": error_msg,
                "traceback": traceback_str,
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
    
    async def _take_screenshot(self, page):
        """Take screenshot with detailed logging"""
        try:
            print(f"   📸 Capturing full-page screenshot...")
            print(f"   💾 Saving to: {SCREENSHOT_FILE}")
            
            await page.screenshot(path=SCREENSHOT_FILE, full_page=True)
            
            # Get file size if possible
            try:
                import os
                if os.path.exists(SCREENSHOT_FILE):
                    file_size = os.path.getsize(SCREENSHOT_FILE)
                    print(f"   ✅ Screenshot saved ({file_size:,} bytes)")
                else:
                    print(f"   ✅ Screenshot completed")
            except:
                print(f"   ✅ Screenshot completed")
            
            self.results["screenshot_taken"] = True
            self.results["screenshot_path"] = SCREENSHOT_FILE
            
        except Exception as e:
            error_msg = f"Screenshot failed: {str(e)}"
            print(f"   ❌ {error_msg}")
            self.results["errors"].append({
                "type": "screenshot_error",
                "message": error_msg,
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
    
    def save_results(self):
        """Save test results to JSON file"""
        try:
            print(f"\n💾 Saving results to: {OUTPUT_FILE}")
            with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, default=str, ensure_ascii=False)
            print(f"✅ Results saved successfully")
        except Exception as e:
            print(f"❌ Failed to save results: {str(e)}")
    
    def print_summary(self):
        """Print comprehensive test summary"""
        print("\n" + "="*80)
        print("📊 TEST EXECUTION SUMMARY")
        print("="*80)
        
        # Overall status
        success_emoji = "✅" if self.results["success"] else "❌"
        status_text = "SUCCESS" if self.results["success"] else "FAILED"
        print(f"{success_emoji} Overall Test Status: {status_text}")
        
        # Connectivity info
        if "connectivity" in self.results:
            conn = self.results["connectivity"]
            status_emoji = "✅" if conn.get("ok", False) else "❌"
            print(f"{status_emoji} HTTP Response: {conn.get('status_code', 'N/A')} {conn.get('status_text', 'N/A')}")
            if conn.get("redirected", False):
                print(f"🔄 Redirected: {conn.get('original_url', '')} → {conn.get('url', '')}")
        
        # Page info
        if "page_info" in self.results:
            page = self.results["page_info"]
            print(f"📄 Page Title: {page.get('title', 'N/A')}")
            
            keywords_emoji = "✅" if page.get("title_contains_expected", False) else "❌"
            matched = page.get("title_matched_keywords", [])
            print(f"{keywords_emoji} Title Keywords: {matched if matched else 'None matched'}")
            
            js_errors = page.get("javascript_error_count", 0)
            js_emoji = "✅" if js_errors == 0 else "⚠️"
            print(f"{js_emoji} JavaScript Errors: {js_errors}")
            
            content_size = page.get("content_length", 0)
            if content_size > 0:
                print(f"📏 Content Size: {content_size:,} characters")
        
        # Performance metrics
        if "performance" in self.results:
            perf = self.results["performance"]
            print(f"⚡ Performance:")
            print(f"   • Browser launch: {perf.get('browser_launch_time', 0):.2f}s")
            print(f"   • Navigation: {perf.get('navigation_time', 0):.2f}s")
            print(f"   • Total test time: {perf.get('total_test_time', 0):.2f}s")
        
        # Screenshot info
        if self.results.get("screenshot_taken", False):
            print(f"📸 Screenshot: Saved to {self.results.get('screenshot_path', 'Unknown')}")
        
        # Error summary
        error_count = len(self.results.get("errors", []))
        if error_count > 0:
            print(f"⚠️  Errors Encountered: {error_count}")
            for i, error in enumerate(self.results.get("errors", []), 1):
                print(f"   {i}. {error.get('type', 'Unknown')}: {error.get('message', 'No message')}")
        
        # Progress summary
        progress_items = self.results.get("progress", [])
        if progress_items:
            completed_steps = len([p for p in progress_items if p.get("success", True)])
            print(f"📋 Progress: {completed_steps}/{len(progress_items)} steps completed")
        
        print("="*80)

async def main():
    """Main execution function with comprehensive error handling"""
    print("Starting SAP Playwright Connectivity Test...")
    print(f"Python version: {os.sys.version}")
    print(f"Working directory: {os.getcwd()}")
    print("")
    
    tester = SAPPlaywrightTester()
    
    try:
        await tester.run_test()
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        tester.results["success"] = False
        tester.results["errors"].append({
            "type": "user_interrupt",
            "message": "Test was interrupted by user (Ctrl+C)",
            "timestamp": datetime.now(timezone.utc).isoformat()
        })
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        print(f"🔍 Error type: {type(e).__name__}")
        traceback_str = traceback.format_exc()
        print(f"📋 Full traceback:")
        print(traceback_str)
        
        tester.results["success"] = False
        tester.results["errors"].append({
            "type": "unexpected_error",
            "message": str(e),
            "error_type": type(e).__name__,
            "traceback": traceback_str,
            "timestamp": datetime.now(timezone.utc).isoformat()
        })
    
    # Always save results and print summary
    tester.save_results()
    tester.print_summary()
    
    # Exit with appropriate code
    exit_code = 0 if tester.results["success"] else 1
    print(f"\n🏁 Exiting with code: {exit_code}")
    exit(exit_code)

if __name__ == "__main__":
    asyncio.run(main()) 