# SAP Playwright Enterprise Tester 🏢

**Production-ready monitoring solution with Azure KeyVault, Blob Storage, and Splunk integration**

Inspiré de l'architecture du projet **8810** avec toutes les fonctionnalités entreprise.

## 🎯 Vue d'ensemble entreprise

Cette version transforme le simple test Playwright en une **solution de monitoring d'entreprise complète** :

### 🔐 **Sécurité niveau entreprise**
- **Azure KeyVault** pour stocker les credentials SAP de manière sécurisée
- **Managed Identity** pour l'authentification sans mot de passe
- **Role-based access control** (RBAC) automatique

### 📦 **Stockage et persistance**
- **Azure Blob Storage** pour screenshots et résultats JSON
- **Logs CSV structurés** compatibles avec les standards entreprise
- **Fallback local** si le cloud est indisponible

### 📊 **Monitoring et observabilité** 
- **Azure Log Analytics** pour les logs de container
- **Splunk integration** pour centraliser les événements
- **Métriques de performance** détaillées
- **Alerting** via Azure Monitor (configurable)

### ⏰ **Exécution automatique**
- **Azure Container Apps** avec scheduler intégré
- **Exécution toutes les 5 minutes** (configurable)
- **Auto-scaling** et **high availability**
- **Infrastructure as Code** avec ARM templates

## 🏗️ Architecture entreprise

```
┌─────────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Azure KeyVault    │    │  Container Apps  │    │  Blob Storage   │
│                     │    │                  │    │                 │
│ SAP Credentials     │◄───┤ SAP Playwright   ├───►│ Screenshots     │
│ Configuration       │    │ Enterprise       │    │ JSON Results    │
│                     │    │ (every 5 min)    │    │ CSV Logs        │
└─────────────────────┘    └──────────────────┘    └─────────────────┘
                                    │
                                    ▼
                           ┌─────────────────┐
                           │ Log Analytics   │
                           │ + Splunk        │
                           │                 │
                           │ Centralized     │
                           │ Monitoring      │
                           └─────────────────┘
```

## 📁 Structure du projet entreprise

```
10001/
├── sap_playwright_enterprise.py    # Script principal avec intégrations Azure
├── requirements-enterprise.txt     # Dépendances avec Azure SDKs
├── Dockerfile-enterprise           # Container avec Azure tooling
├── template-enterprise.json        # ARM template Infrastructure as Code
├── parameters-enterprise.json      # Paramètres de déploiement
├── deploy-enterprise.sh            # Script de déploiement complet
└── README-enterprise.md            # Ce fichier

# Versions simples (pour comparaison)
├── sap_playwright_tester.py        # Version basique
├── requirements.txt                # Dépendances simples
├── Dockerfile                      # Container simple
├── deploy-sap-playwright.sh        # Déploiement basique
└── README.md                       # Documentation basique
```

## 🚀 Déploiement entreprise

### Prérequis
- Azure CLI installé et configuré
- Docker installé
- Permissions pour créer des ressources Azure
- KeyVault et Storage Account existants (ou créés par le template)

### 1. Configuration des paramètres
Éditez `parameters-enterprise.json` avec vos valeurs :

```json
{
    "keyVaultName": {"value": "votre-keyvault-name"},
    "storageAccountName": {"value": "votre-storage-account"},
    "containerRegistryName": {"value": "scriptmonregistry2025"},
    "sapTargetUrl": {"value": "https://votre-sap-url.com/..."},
    "splunkUrl": {"value": "https://splunk.company.com:8088/services/collector"},
    "scheduleCron": {"value": "*/5 * * * *"}
}
```

### 2. Configuration des credentials SAP dans KeyVault

```bash
# Créer le secret avec la configuration SAP
az keyvault secret set \
    --vault-name VOTRE_KEYVAULT \
    --name SAP-LAUNCHPAD-CONFIG \
    --value '{
        "url": "https://oneb-central-dev.launchpad.cfapps.eu10.hana.ondemand.com/site?siteId=1af3caee-8117-49e3-b787-7ad74914b5cb",
        "username": "votre-username",
        "password": "votre-password", 
        "expected_keywords": ["launchpad", "sap", "fiori"]
    }'
```

### 3. Déploiement complet

```bash
cd 10001
wsl chmod +x deploy-enterprise.sh
wsl -e bash -c "./deploy-enterprise.sh"
```

Le script va :
1. 🔨 **Builder** l'image enterprise avec Azure SDKs
2. 📤 **Pousser** vers votre Container Registry
3. 🏗️ **Déployer** l'infrastructure complète via ARM template
4. 🔐 **Configurer** les permissions RBAC automatiquement
5. ⏰ **Lancer** l'exécution toutes les 5 minutes

## 📊 Monitoring et observabilité

### Azure Log Analytics
```bash
# Voir les logs en temps réel
az containerapp logs show \
    --name sap-playwright-enterprise-10001 \
    --resource-group RG-EDGMON-RUN \
    --follow
```

### Blob Storage - Structure des données
```
sap-monitoring-results/
├── screenshots/
│   ├── 10001_screenshot_20240722_143022.png
│   └── 10001_screenshot_20240722_143527.png
├── results/
│   ├── 10001_results_20240722_143022.json
│   └── 10001_results_20240722_143527.json
└── logs/
    ├── 10001_SAP_PLAYWRIGHT_20240722.csv
    └── splunk_events.jsonl
```

### Format des logs CSV (compatible Splunk)
```csv
SCRIPT_ID;SCRIPT_DATE;SCRIPT_TIME;TEST_STATUS;LOAD_TIME;HTTP_STATUS;JS_ERRORS;SCREENSHOT_STATUS;
10001;22.07.2024;14:30:22;OK;3.45s;200;0;OK;
10001;22.07.2024;14:35:27;NOK;timeout;500;2;OK;
```

### Événements Splunk
```json
{
    "time": **********,
    "source": "sap_playwright_monitor", 
    "sourcetype": "sap_monitoring",
    "event": {
        "script_id": "10001",
        "test_url": "https://...",
        "success": true,
        "performance": {"total_test_time": 8.67},
        "connectivity": {"status_code": 200}
    }
}
```

## ⚙️ Configuration avancée

### Variables d'environnement (automatiques via ARM template)
```bash
# Azure Integration
KEY_VAULT_NAME=votre-keyvault
STORAGE_ACCOUNT_NAME=votre-storage
SAP_KEYVAULT_SECRET=SAP-LAUNCHPAD-CONFIG

# Test Configuration  
TARGET_URL=https://...
PAGE_TIMEOUT=60000
BROWSER_TYPE=chromium
TAKE_SCREENSHOT=true

# Splunk Integration
SPLUNK_ENABLED=true
SPLUNK_URL=https://splunk.company.com:8088/services/collector
SPLUNK_TOKEN=your-hec-token
```

### Modification du scheduling
```json
// Dans template-enterprise.json
"scheduleCron": {
    "defaultValue": "*/5 * * * *"  // Toutes les 5 minutes
    // "0 */2 * * *"               // Toutes les 2 heures
    // "0 8,12,16,20 * * *"        // 4 fois par jour
    // "0 9 * * 1-5"               // 9h du lundi au vendredi
}
```

## 🔍 Dépannage entreprise

### Vérifier les permissions
```bash
# Vérifier que la Managed Identity a les permissions
az role assignment list \
    --assignee $(az containerapp show \
        --name sap-playwright-enterprise-10001 \
        --resource-group RG-EDGMON-RUN \
        --query identity.principalId -o tsv)
```

### Tester KeyVault manuellement
```bash
# Tester l'accès au secret
az keyvault secret show \
    --vault-name VOTRE_KEYVAULT \
    --name SAP-LAUNCHPAD-CONFIG
```

### Logs détaillés de déploiement
```bash
# Voir les événements de déploiement
az deployment group show \
    --resource-group RG-EDGMON-RUN \
    --name DEPLOYMENT_NAME \
    --query properties.provisioningState
```

## 📈 Métriques et alerting

### Métriques disponibles dans Azure Monitor
- **Execution Count** - Nombre d'exécutions
- **Success Rate** - Taux de réussite  
- **Response Time** - Temps de réponse SAP
- **Error Count** - Nombre d'erreurs
- **Screenshot Success** - Réussite des captures

### Alertes recommandées
```bash
# Alerte si le taux de réussite < 80%
az monitor metrics alert create \
    --name "SAP Playwright Success Rate" \
    --resource-group RG-EDGMON-RUN \
    --condition "avg Percentage < 80" \
    --description "SAP Playwright success rate below 80%"
```

## 🏢 Avantages entreprise vs version basique

| Aspect | Version Basique | **Version Entreprise** |
|--------|-----------------|------------------------|
| **Sécurité** | Credentials en dur | **🔐 Azure KeyVault** |
| **Stockage** | Logs locaux | **📦 Azure Blob Storage** |
| **Scheduling** | Manual | **⏰ Azure Container Apps (5 min)** |
| **Monitoring** | Basique | **📊 Splunk + Log Analytics** |
| **Déploiement** | Script bash | **🏗️ ARM Template IaC** |
| **Permissions** | Manuelles | **🛡️ RBAC automatique** |
| **Scalabilité** | Container unique | **📈 Auto-scaling** |
| **Observabilité** | Logs container | **📊 Métriques + Alertes** |
| **Entreprise** | Non | **✅ Production-ready** |

## 🎯 Use cases entreprise

### 1. **Monitoring SAP 24/7**
- Détection proactive des pannes SAP
- SLA monitoring avec alertes automatiques
- Métriques de performance en temps réel

### 2. **Compliance et audit**
- Logs centralisés dans Splunk
- Traçabilité complète des tests
- Rapports automatiques pour le management

### 3. **DevOps Integration**
- Infrastructure as Code reproductible
- Pipeline CI/CD intégrable
- Multi-environment (dev/staging/prod)

### 4. **Business Intelligence**
- Dashboard Splunk avec KPIs
- Trends analysis des performances SAP
- Alerting métier sur les indisponibilités

---

**🏢 Projet 10001 Enterprise - Solution complète de monitoring SAP avec architecture Azure native** 