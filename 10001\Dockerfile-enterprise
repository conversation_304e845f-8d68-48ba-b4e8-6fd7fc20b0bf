# Enterprise SAP Playwright Tester with Azure Integration
FROM mcr.microsoft.com/playwright/python:v1.40.0-jammy

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    iputils-ping \
    dnsutils \
    jq \
    && rm -rf /var/lib/apt/lists/*

# Copy enterprise requirements
COPY requirements-enterprise.txt requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Install Playwright browsers
RUN playwright install chromium

# Copy enterprise script
COPY sap_playwright_enterprise.py .

# Create directories for enterprise results
RUN mkdir -p /app/results /app/screenshots /app/logs

# Set executable permissions
RUN chmod +x sap_playwright_enterprise.py

# Verify installation
RUN ls -la /app/ && python --version

# Run enterprise script
CMD ["python", "sap_playwright_enterprise.py"] 