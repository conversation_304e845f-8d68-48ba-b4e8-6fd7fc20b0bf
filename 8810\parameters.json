{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"containerAppName": {"value": "monscript8810"}, "location": {"value": "germanywestcentral"}, "keyVaultName": {"value": "b2smepcw-kv-12345"}, "storageAccountName": {"value": "b2smepcwsa12345"}, "containerRegistryName": {"value": "edgmon4546"}, "containerImageName": {"value": "puw-monitoring:latest"}, "scheduleCron": {"value": "0 */2 * * *"}}}