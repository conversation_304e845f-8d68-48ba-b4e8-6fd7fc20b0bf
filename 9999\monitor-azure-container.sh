#!/bin/bash

# Azure Container Monitor Script
# Monitor execution and retrieve logs from Azure Container Instances

set -e

# Configuration - Using your existing setup
RESOURCE_GROUP="RG-EDGMON-RUN"

echo "🔍 Azure Container Monitor - Network Tester"
echo "=" * 60

# Function to list all network-tester containers
list_containers() {
    echo "📋 Listing all network-tester containers..."
    az container list \
        --resource-group $RESOURCE_GROUP \
        --query "[?contains(name, 'network-tester')].{Name:name, Status:provisioningState, RestartPolicy:restartPolicy, CreatedTime:creationTime}" \
        --output table
}

# Function to get detailed status of a specific container
get_container_status() {
    local container_name=$1
    echo "🔍 Detailed status for container: $container_name"
    echo "-" * 40
    
    az container show \
        --resource-group $RESOURCE_GROUP \
        --name $container_name \
        --query "{Name:name, Status:provisioningState, State:containers[0].instanceView.currentState.state, StartTime:containers[0].instanceView.currentState.startTime, FinishTime:containers[0].instanceView.currentState.finishTime, ExitCode:containers[0].instanceView.currentState.exitCode, RestartCount:containers[0].instanceView.restartCount, CPU:containers[0].resources.requests.cpu, Memory:containers[0].resources.requests.memoryInGB}" \
        --output table
}

# Function to get container logs
get_container_logs() {
    local container_name=$1
    echo "📋 Logs for container: $container_name"
    echo "-" * 40
    
    az container logs \
        --resource-group $RESOURCE_GROUP \
        --name $container_name
}

# Function to monitor container in real-time
monitor_container() {
    local container_name=$1
    echo "📊 Monitoring container: $container_name"
    echo "Press Ctrl+C to stop monitoring"
    echo "-" * 40
    
    while true; do
        echo "🕒 $(date '+%Y-%m-%d %H:%M:%S')"
        
        # Get current state
        local state=$(az container show \
            --resource-group $RESOURCE_GROUP \
            --name $container_name \
            --query "containers[0].instanceView.currentState.state" \
            --output tsv 2>/dev/null || echo "Not Found")
        
        echo "   State: $state"
        
        if [ "$state" = "Terminated" ] || [ "$state" = "Not Found" ]; then
            echo "✅ Container has finished execution"
            break
        fi
        
        sleep 10
    done
}

# Function to find the latest network-tester container
find_latest_container() {
    az container list \
        --resource-group $RESOURCE_GROUP \
        --query "[?contains(name, 'network-tester') && creationTime != null] | sort_by(@, &creationTime) | [-1].name" \
        --output tsv
}

# Main menu
show_menu() {
    echo ""
    echo "🎯 What would you like to do?"
    echo "1. List all network-tester containers"
    echo "2. Monitor latest container (real-time)"
    echo "3. Get status of latest container"
    echo "4. Get logs of latest container"
    echo "5. Get status of specific container"
    echo "6. Get logs of specific container"
    echo "7. Complete report of latest container"
    echo "0. Exit"
    echo ""
    read -p "Choose an option (0-7): " choice
}

# Main execution
case "${1:-menu}" in
    "list")
        list_containers
        ;;
    "latest-status")
        LATEST_CONTAINER=$(find_latest_container)
        if [ -n "$LATEST_CONTAINER" ]; then
            get_container_status "$LATEST_CONTAINER"
        else
            echo "❌ No network-tester containers found"
        fi
        ;;
    "latest-logs")
        LATEST_CONTAINER=$(find_latest_container)
        if [ -n "$LATEST_CONTAINER" ]; then
            get_container_logs "$LATEST_CONTAINER"
        else
            echo "❌ No network-tester containers found"
        fi
        ;;
    "latest-report")
        LATEST_CONTAINER=$(find_latest_container)
        if [ -n "$LATEST_CONTAINER" ]; then
            echo "🎯 Complete Report for: $LATEST_CONTAINER"
            echo "=" * 60
            get_container_status "$LATEST_CONTAINER"
            echo ""
            get_container_logs "$LATEST_CONTAINER"
        else
            echo "❌ No network-tester containers found"
        fi
        ;;
    "monitor")
        LATEST_CONTAINER=$(find_latest_container)
        if [ -n "$LATEST_CONTAINER" ]; then
            monitor_container "$LATEST_CONTAINER"
        else
            echo "❌ No network-tester containers found"
        fi
        ;;
    "menu"|*)
        while true; do
            show_menu
            case $choice in
                1)
                    list_containers
                    ;;
                2)
                    LATEST_CONTAINER=$(find_latest_container)
                    if [ -n "$LATEST_CONTAINER" ]; then
                        monitor_container "$LATEST_CONTAINER"
                    else
                        echo "❌ No network-tester containers found"
                    fi
                    ;;
                3)
                    LATEST_CONTAINER=$(find_latest_container)
                    if [ -n "$LATEST_CONTAINER" ]; then
                        get_container_status "$LATEST_CONTAINER"
                    else
                        echo "❌ No network-tester containers found"
                    fi
                    ;;
                4)
                    LATEST_CONTAINER=$(find_latest_container)
                    if [ -n "$LATEST_CONTAINER" ]; then
                        get_container_logs "$LATEST_CONTAINER"
                    else
                        echo "❌ No network-tester containers found"
                    fi
                    ;;
                5)
                    list_containers
                    echo ""
                    read -p "Enter container name: " container_name
                    if [ -n "$container_name" ]; then
                        get_container_status "$container_name"
                    fi
                    ;;
                6)
                    list_containers
                    echo ""
                    read -p "Enter container name: " container_name
                    if [ -n "$container_name" ]; then
                        get_container_logs "$container_name"
                    fi
                    ;;
                7)
                    LATEST_CONTAINER=$(find_latest_container)
                    if [ -n "$LATEST_CONTAINER" ]; then
                        echo "🎯 Complete Report for: $LATEST_CONTAINER"
                        echo "=" * 60
                        get_container_status "$LATEST_CONTAINER"
                        echo ""
                        get_container_logs "$LATEST_CONTAINER"
                    else
                        echo "❌ No network-tester containers found"
                    fi
                    ;;
                0)
                    echo "👋 Goodbye!"
                    exit 0
                    ;;
                *)
                    echo "❌ Invalid option. Please choose 0-7."
                    ;;
            esac
            echo ""
            read -p "Press Enter to continue..."
        done
        ;;
esac 