from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from web_tester import WebTester
import os

app = FastAPI(title="Web Tester API", version="1.0.0")

class TestRequest(BaseModel):
    url: str
    timeout: int = 30

class TestResponse(BaseModel):
    results: dict

@app.get("/")
async def root():
    return {"message": "Web Tester API", "status": "running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "web-tester"}

@app.post("/test", response_model=TestResponse)
async def test_url(request: TestRequest):
    """Teste une URL et retourne les métriques de performance"""
    try:
        tester = WebTester(request.url, request.timeout)
        results = tester.run_full_test()
        return TestResponse(results=results)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/test/{encoded_url}")
async def test_url_get(encoded_url: str, timeout: int = 30):
    """Teste une URL encodée via GET"""
    try:
        # Décodage basique de l'URL
        import urllib.parse
        url = urllib.parse.unquote(encoded_url)
        
        tester = WebTester(url, timeout)
        results = tester.run_full_test()
        return {"results": results}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/quick-test")
async def quick_test():
    """Test rapide avec l'URL par défaut"""
    default_url = os.getenv('DEFAULT_URL', 'https://www.google.com')
    try:
        tester = WebTester(default_url, 10)  # Timeout réduit pour test rapide
        results = tester.run_full_test()
        return {"results": results}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)