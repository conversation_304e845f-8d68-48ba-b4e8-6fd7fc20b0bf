from playwright.sync_api import <PERSON><PERSON>, sync_playwright, expect
import os
import sys
import time
from datetime import date
import datetime as dt
from datetime import datetime
import pyodbc
import uuid
from azure.keyvault.secrets import SecretClient
from azure.identity import DefaultAzureCredential
from azure.storage.fileshare import ShareFileClient, ShareDirectoryClient
import tempfile
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AzureEonMonitoring:
    def __init__(self):
        # Configuration Azure
        self.keyvault_name = "scriptsmonitoring2025"
        self.keyvault_url = f"https://{self.keyvault_name}.vault.azure.net/"
        self.storage_account_name = "scriptsmonitoring2025"
        self.file_share_name = "script-monitoring"
        self.container_name = "3111"
        
        # Initialisation des clients Azure avec retry
        try:
            self.credential = DefaultAzureCredential()
            self.secret_client = SecretClient(vault_url=self.keyvault_url, credential=self.credential)
            logger.info("Azure clients initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Azure clients: {str(e)}")
            raise
        
        # Chemins locaux
        self.local_results_path = "/tmp/monitoring/results"  # Utiliser /tmp au lieu de /mnt
        self.local_img_path = "/tmp/monitoring/img"
        
        # Créer les répertoires locaux
        os.makedirs(self.local_results_path, exist_ok=True)
        os.makedirs(self.local_img_path, exist_ok=True)
        
        logger.info("Azure Eon Monitoring initialized")

    def get_secret(self, secret_name):
        """Récupère un secret depuis Azure Key Vault"""
        try:
            retrieved_secret = self.secret_client.get_secret(secret_name)
            logger.info(f"Secret {secret_name} retrieved successfully")
            return retrieved_secret.value
        except Exception as e:
            logger.error(f"Erreur lors de la récupération du secret {secret_name}: {str(e)}")
            return None

    def create_azure_directory_if_not_exists(self, directory_path):
        """Crée un répertoire dans Azure File Share s'il n'existe pas"""
        try:
            dir_client = ShareDirectoryClient(
                account_url=f"https://{self.storage_account_name}.file.core.windows.net",
                share_name=self.file_share_name,
                directory_path=directory_path,
                credential=self.credential
            )
            
            # Créer le répertoire s'il n'existe pas
            if not dir_client.exists():
                dir_client.create_directory()
                logger.info(f"Directory created: {directory_path}")
            
            return True
        except Exception as e:
            logger.error(f"Error creating directory {directory_path}: {str(e)}")
            return False

    def upload_to_azure_storage(self, local_file_path, remote_file_path):
        """Upload un fichier vers Azure File Share"""
        try:
            # Créer les répertoires nécessaires
            directory_path = os.path.dirname(remote_file_path)
            if directory_path:
                self.create_azure_directory_if_not_exists(directory_path)
            
            file_client = ShareFileClient(
                account_url=f"https://{self.storage_account_name}.file.core.windows.net",
                share_name=self.file_share_name,
                file_path=remote_file_path,
                credential=self.credential
            )
            
            with open(local_file_path, "rb") as data:
                file_client.upload_file(data, overwrite=True)
            
            logger.info(f"File uploaded successfully: {remote_file_path}")
            return True
        except Exception as e:
            logger.error(f"Error uploading file {local_file_path}: {str(e)}")
            return False

    def log(self, sLine):
        """Log les résultats localement et sur Azure"""
        try:
            # Créer le fichier local
            local_file_path = os.path.join(self.local_results_path, "3111_RTR.csv")
            
            # Vérifier si le fichier existe, sinon créer le header
            file_exists = os.path.exists(local_file_path)
            if not file_exists:
                with open(local_file_path, 'w') as datei:
                    sHeader = "SCRIPT_ID;SCRIPT_DATE;SCRIPT_START_TIME;LOAD_LOGIN_PAGE;LOGIN;METER_READING;ABSCHLAG;LOGOUT;\n"
                    datei.write(sHeader)
                logger.info("CSV header created")

            # Ajouter la ligne de données
            with open(local_file_path, 'a') as datei:
                formatted_line = "3111;" + date.today().strftime("%d.%m.%Y") + ";" + \
                    datetime.now().strftime("%H:%M:%S") + ";" + sLine + "\n"
                datei.write(formatted_line)

            logger.info(f"Data written to local CSV: {sLine}")

            # Upload vers Azure File Share
            remote_path = f"{self.container_name}/3111_RTR.csv"
            upload_success = self.upload_to_azure_storage(local_file_path, remote_path)
            
            if upload_success:
                logger.info("Results successfully uploaded to Azure Storage")
            else:
                logger.error("Failed to upload results to Azure Storage")
            
        except Exception as e:
            logger.error(f"Error in log function: {str(e)}")

    def logdb(self, sLoginValue):
        """Log vers Azure SQL Database"""
        try:
            # Récupérer les credentials de la DB depuis Key Vault
            db_server = self.get_secret("db-server")
            db_name = self.get_secret("db-name") 
            db_username = self.get_secret("db-username")
            db_password = self.get_secret("db-password")
            
            if not all([db_server, db_name, db_username, db_password]):
                logger.error("Missing database credentials from Key Vault")
                return
            
            # Connection string pour Azure SQL Database
            conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={db_server};DATABASE={db_name};UID={db_username};PWD={db_password};Encrypt=yes;TrustServerCertificate=no;Connection Timeout=30;'
            
            conn = pyodbc.connect(conn_str)
            cursor = conn.cursor()

            MID = str(uuid.uuid4())
            TIMESTAMP = dt.datetime.now().strftime("%Y-%m-%d %H:%M:%S") + ".000"

            # Insérer dans la table Measurement
            sql_measurement = "INSERT INTO Measurement (MID,SCRIPTID,TIMESTAMP,STATIONID) VALUES (?,?,?,?)"
            cursor.execute(sql_measurement, (MID, '3111', TIMESTAMP, 200))
            
            # Insérer dans la table RESULT
            sql_result = "INSERT INTO RESULT (MID,NAME,VALUE) VALUES (?,?,?)"
            cursor.execute(sql_result, (MID, 'LOGIN', sLoginValue))
            
            conn.commit()
            cursor.close()
            conn.close()
            
            logger.info(f"Data recorded in database: Login time = {sLoginValue}ms")
            
        except Exception as e:
            logger.error(f"Database logging error: {str(e)}")

    def GetMS(self, fTime):
        """Convertit le temps en millisecondes"""
        if fTime <= 0:
            return "0"
        m1 = int(round(fTime * 1000, 0))
        return str(m1)

    def take_screenshot(self, page, step_name):
        """Prend un screenshot et l'upload vers Azure"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
            filename = f"3111_{timestamp}_{step_name}.png"
            local_path = os.path.join(self.local_img_path, filename)
            
            page.screenshot(path=local_path)
            logger.info(f"Screenshot taken: {filename}")
            
            # Upload vers Azure
            remote_path = f"{self.container_name}/screenshots/{filename}"
            upload_success = self.upload_to_azure_storage(local_path, remote_path)
            
            if upload_success:
                logger.info(f"Screenshot uploaded: {filename}")
            
            return local_path
            
        except Exception as e:
            logger.error(f"Screenshot error: {str(e)}")
            return None

    def run(self, playwright: Playwright) -> None:
        """Fonction principale d'exécution du monitoring"""
        logger.info("=== STARTING EON MONITORING ===")
        
        try:
            # Récupérer les credentials depuis Key Vault
            email = self.get_secret("eon-email")
            password = self.get_secret("eon-password")
            
            if not email or not password:
                logger.error("Unable to retrieve credentials from Key Vault")
                sys.exit(1)

            logger.info(f"Credentials retrieved for email: {email[:5]}***")

            # Configuration du navigateur
            browser = playwright.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-extensions',
                    '--disable-background-timer-throttling',
                    '--disable-renderer-backgrounding',
                    '--disable-backgrounding-occluded-windows'
                ]
            )
            
            context = browser.new_context(
                user_agent="<EMAIL>?9185",
                viewport={'width': 1280, 'height': 720}
            )
            page = context.new_page()
            
            # Configuration timeout global
            page.set_default_timeout(60000)  # 60 secondes
            
            bLoginPageError = False
            bContinue = True
            
            # Métriques de performance
            metrics = {
                'load_login_page': 0,
                'login': 0,
                'meter_reading': 0,
                'abschlag': 0,
                'logout': 0
            }

            ############################################################################
            # Load Login Page
            ############################################################################
            logger.info("Step 1: Loading login page")
            start = time.time()

            try:
                page.goto("https://da.eon.de/s/login/", wait_until='networkidle')
                
                try:
                    page.get_by_test_id("uc-accept-all-button").click(timeout=3000)
                    logger.info("Cookie banner accepted")
                except:
                    logger.info("No cookie banner found")
                
                page.get_by_role("textbox", name="E-Mail-Adresse").fill(email)
                page.get_by_role("textbox", name="Passwort").fill(password)
                
                stop = time.time()
                metrics['load_login_page'] = stop - start
                logger.info(f"Login page loaded in {self.GetMS(metrics['load_login_page'])}ms")

            except Exception as e:
                logger.error(f"Login page loading error: {str(e)}")
                metrics['load_login_page'] = 0
                bContinue = False
                bLoginPageError = True
                self.take_screenshot(page, "LOAD_LOGIN_PAGE_ERROR")

            ############################################################################
            # Perform Login
            ############################################################################
            if bContinue:
                logger.info("Step 2: Performing login")
                start = time.time()
                
                try:
                    page.get_by_role("button", name="Anmelden").click()
                    page.wait_for_url("https://www.eon.de/de/meineonda/meine-uebersicht.html", timeout=30000)

                    try:
                        page.get_by_text("Ablehnen").click(timeout=5000)
                        logger.info("Additional consent declined")
                    except:
                        logger.info("No additional consent dialog")

                    page.locator("login-dashboard-app").get_by_text("Rechnung").first.click(timeout=30000)
                    
                    stop = time.time()
                    metrics['login'] = stop - start
                    logger.info(f"Login successful in {self.GetMS(metrics['login'])}ms")

                except Exception as e:
                    logger.error(f"Login error: {str(e)}")
                    metrics['login'] = 0
                    bContinue = False
                    self.take_screenshot(page, "LOGIN_ERROR")

            ############################################################################
            # Test Zählerstand (Meter Reading)
            ############################################################################
            if bContinue:
                logger.info("Step 3: Testing meter reading")
                start = time.time()
                
                try:
                    page.goto("https://www.eon.de/de/meineonda/mein-zaehlerstand.html", wait_until='networkidle')
                    page.locator("login-meter-read-submission-app").get_by_text("Zählernummer: 9701696").click(timeout=30000)
                    
                    stop = time.time()
                    metrics['meter_reading'] = stop - start
                    logger.info(f"Meter reading test successful in {self.GetMS(metrics['meter_reading'])}ms")

                except Exception as e:
                    logger.error(f"Meter reading test error: {str(e)}")
                    metrics['meter_reading'] = 0

            ############################################################################
            # Test Abschlag (Installment)
            ############################################################################
            if bContinue:
                logger.info("Step 4: Testing installments")
                start = time.time()
                
                try:
                    page.goto("https://www.eon.de/de/meineonda/mein-abschlag.html", wait_until='networkidle')
                    page.get_by_text("Abschlagseingabe").click(timeout=30000)
                    
                    stop = time.time()
                    metrics['abschlag'] = stop - start
                    logger.info(f"Installment test successful in {self.GetMS(metrics['abschlag'])}ms")

                except Exception as e:
                    logger.error(f"Installment test error: {str(e)}")
                    metrics['abschlag'] = 0

            ############################################################################
            # Logout
            ############################################################################
            logger.info("Step 5: Logging out")
            start = time.time()
            
            try:
                page.get_by_role("menuitem", name="Navigiere zu https://www.eon.de/de/pk.html").locator("svg").click()
                page.get_by_text("Sie haben sich erfolgreich abgemeldet.").click(timeout=15000)
                
                stop = time.time()
                metrics['logout'] = stop - start
                logger.info(f"Logout successful in {self.GetMS(metrics['logout'])}ms")

            except Exception as e:
                logger.error(f"Logout error: {str(e)}")
                metrics['logout'] = 0

            # Fermeture du navigateur
            context.close()
            browser.close()
            logger.info("Browser closed")

            # Enregistrement des résultats
            if not bLoginPageError:
                log_line = f"{self.GetMS(metrics['load_login_page'])};{self.GetMS(metrics['login'])};{self.GetMS(metrics['meter_reading'])};{self.GetMS(metrics['abschlag'])};{self.GetMS(metrics['logout'])};"
                self.log(log_line)
                
                # Log en DB uniquement le temps de login
                if metrics['login'] > 0:
                    self.logdb(self.GetMS(metrics['login']))
                    
                logger.info("Results recorded successfully")
            else:
                logger.error("Execution failed - no results recorded")

        except Exception as e:
            logger.error(f"Critical error during monitoring: {str(e)}")
            sys.exit(1)
            
        finally:
            logger.info("=== EON MONITORING COMPLETED ===")

def main():
    """Point d'entrée principal"""
    try:
        logger.info("Starting EON Monitoring Application")
        monitoring = AzureEonMonitoring()
        
        with sync_playwright() as playwright:
            monitoring.run(playwright)
            
        logger.info("EON Monitoring Application completed successfully")
        
    except Exception as e:
        logger.error(f"Application failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()