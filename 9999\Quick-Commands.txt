# Quick Commands for Azure Container Monitoring
# Copy and paste these commands directly into PowerShell/CMD

# === QUICK ACCESS COMMANDS ===

# 1. List all network-tester containers
az container list --resource-group RG-EDGMON-RUN --query "[?contains(name, 'network-tester')].{Name:name, Status:provisioningState, State:containers[0].instanceView.currentState.state, CreatedTime:creationTime}" --output table

# 2. Get logs from the latest container (replace CONTAINER_NAME with actual name)
az container logs --resource-group RG-EDGMON-RUN --name CONTAINER_NAME

# 3. Get detailed status of a specific container
az container show --resource-group RG-EDGMON-RUN --name CONTAINER_NAME --query "{Name:name, Status:provisioningState, State:containers[0].instanceView.currentState.state, StartTime:containers[0].instanceView.currentState.startTime, FinishTime:containers[0].instanceView.currentState.finishTime, ExitCode:containers[0].instanceView.currentState.exitCode}" --output table

# 4. Find the latest network-tester container name
az container list --resource-group RG-EDGMON-RUN --query "[?contains(name, 'network-tester')] | sort_by(@, &creationTime) | [-1].name" --output tsv

# 5. Delete a finished container (cleanup)
az container delete --resource-group RG-EDGMON-RUN --name CONTAINER_NAME --yes

# === ONE-LINER FOR LATEST CONTAINER LOGS ===
# Get the name of the latest container and show its logs
$latest = az container list --resource-group RG-EDGMON-RUN --query "[?contains(name, 'network-tester')] | sort_by(@, &creationTime) | [-1].name" --output tsv; az container logs --resource-group RG-EDGMON-RUN --name $latest

# === ONE-LINER FOR LATEST CONTAINER STATUS ===
# Get the name of the latest container and show its status
$latest = az container list --resource-group RG-EDGMON-RUN --query "[?contains(name, 'network-tester')] | sort_by(@, &creationTime) | [-1].name" --output tsv; az container show --resource-group RG-EDGMON-RUN --name $latest --query "{Name:name, Status:provisioningState, State:containers[0].instanceView.currentState.state, ExitCode:containers[0].instanceView.currentState.exitCode}" --output table

# === MONITORING WHILE RUNNING ===
# Watch container status every 10 seconds (Ctrl+C to stop)
while ($true) { $latest = az container list --resource-group RG-EDGMON-RUN --query "[?contains(name, 'network-tester')] | sort_by(@, &creationTime) | [-1].name" --output tsv; $state = az container show --resource-group RG-EDGMON-RUN --name $latest --query "containers[0].instanceView.currentState.state" --output tsv; Write-Host "$(Get-Date): Container $latest is $state"; if ($state -eq "Terminated") { break }; Start-Sleep 10 }

# === CLEANUP COMMANDS ===
# List all containers to cleanup
az container list --resource-group RG-EDGMON-RUN --query "[?contains(name, 'network-tester')].{Name:name, Status:provisioningState, State:containers[0].instanceView.currentState.state}" --output table

# Delete all terminated network-tester containers
az container list --resource-group RG-EDGMON-RUN --query "[?contains(name, 'network-tester') && containers[0].instanceView.currentState.state == 'Terminated'].name" --output tsv | ForEach-Object { az container delete --resource-group RG-EDGMON-RUN --name $_ --yes }

# === USAGE EXAMPLES ===
# To use the PowerShell script:
# .\Monitor-AzureContainer.ps1                    # Interactive menu
# .\Monitor-AzureContainer.ps1 -Action list       # List containers
# .\Monitor-AzureContainer.ps1 -Action latest-logs # Get latest logs
# .\Monitor-AzureContainer.ps1 -Action latest-status # Get latest status
# .\Monitor-AzureContainer.ps1 -Action latest-report # Complete report

# To use the Bash script (if you have WSL or Git Bash):
# ./monitor-azure-container.sh                    # Interactive menu
# ./monitor-azure-container.sh list              # List containers
# ./monitor-azure-container.sh latest-logs       # Get latest logs
# ./monitor-azure-container.sh latest-status     # Get latest status 