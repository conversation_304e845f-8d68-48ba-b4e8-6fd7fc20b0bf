# SAP Playwright Tester - Project 10001 🎯

**Enhanced Version avec logs détaillés et monitoring complet**

## 🚀 Vue d'ensemble

Ce projet teste automatiquement l'accessibilité et la performance d'un SAP Launchpad en utilisant Playwright dans un container Azure. Contrairement au projet 9999, celui-ci utilise un **vrai navigateur** pour des tests plus réalistes.

### 🎯 URL testée
```
https://oneb-central-dev.launchpad.cfapps.eu10.hana.ondemand.com/site?siteId=1af3caee-8117-49e3-b787-7ad74914b5cb
```

## ✨ Fonctionnalités améliorées

### 📊 **Logs détaillés et suivi de progress**
- ✅ Suivi étape par étape (6 étapes bien définies)
- ✅ Temps d'exécution pour chaque phase
- ✅ Messages de progression clairs avec emojis
- ✅ Traceback complet en cas d'erreur

### 🔍 **Tests complets**
- ✅ **Test de connectivité HTTP** - Status codes, redirections
- ✅ **Analyse de page** - Titre, contenu, JavaScript errors
- ✅ **Screenshot full-page** - Capture complète de la page
- ✅ **Métriques de performance** - Temps de chargement, DOM ready
- ✅ **Rapport JSON détaillé** - Toutes les données structurées

### 🛠️ **Monitoring avancé**
- ✅ Script de monitoring **corrigé** (trouve correctement le dernier container)
- ✅ Multiple méthodes de recherche de containers
- ✅ Monitoring en temps réel avec logs actualisés
- ✅ Résumé complet des tests avec interprétation

## 🚀 Déploiement rapide

### 1. Déployer le container
```bash
cd 10001
wsl chmod +x *.sh  # Si nécessaire sur Windows+WSL
./deploy-sap-playwright.sh
```

### 2. Suivre l'exécution
```bash
# Menu interactif complet
./monitor-sap-playwright.sh

# Ou commandes directes:
./monitor-sap-playwright.sh latest-summary    # Résumé complet
./monitor-sap-playwright.sh monitor          # Temps réel
./monitor-sap-playwright.sh latest-logs      # Logs uniquement
```

## 📁 Structure du projet

```
10001/
├── sap_playwright_tester.py    # Script Python avec logs détaillés
├── requirements.txt            # Playwright uniquement
├── Dockerfile                  # Image Playwright officielle
├── deploy-sap-playwright.sh    # Déploiement Azure
├── monitor-sap-playwright.sh   # Monitoring corrigé
└── README.md                   # Ce fichier
```

## ⚙️ Configuration complète

### Variables d'environnement (supportées)
```bash
# URL de test
TARGET_URL="https://oneb-central-dev.launchpad.cfapps.eu10.hana.ondemand.com/site?siteId=1af3caee-8117-49e3-b787-7ad74914b5cb"

# Timeouts (en millisecondes)
PAGE_TIMEOUT=60000           # 60 secondes
NAVIGATION_TIMEOUT=30000     # 30 secondes
WAIT_TIMEOUT=10000          # 10 secondes

# Browser settings
BROWSER_TYPE=chromium        # chromium, firefox, webkit
HEADLESS=true               # true/false
VIEWPORT_WIDTH=1920
VIEWPORT_HEIGHT=1080

# Outputs
TAKE_SCREENSHOT=true        # true/false
OUTPUT_FILE=/app/sap_playwright_test_results.json
SCREENSHOT_FILE=/app/sap_screenshot.png

# Test parameters
EXPECTED_TITLE_KEYWORDS=launchpad,sap,fiori
WAIT_FOR_SELECTOR=""        # CSS selector optionnel
```

### Variables du script de déploiement
```bash
# Dans deploy-sap-playwright.sh
RESOURCE_GROUP="RG-EDGMON-RUN"
ACR_NAME="scriptmonregistry2025"
SAP_URL="https://oneb-central-dev.launchpad.cfapps.eu10.hana.ondemand.com/..."
```

## 📊 Exemple de logs détaillés

```
🚀 Starting SAP Playwright test for: https://oneb-central-dev.launchpad...
🕒 Test started at: 2024-01-20T10:30:00
⚙️  Configuration:
   • Browser: chromium
   • Headless: true
   • Viewport: 1920x1080
   • Page Timeout: 60000ms
   • Screenshot: true

✅ 1/6: Initializing Playwright environment
✅ 2/6: Browser launched in 2.34s
✅ 3/6: Page created and configured successfully
✅ 4/6: Navigation completed in 3.12s (Status: 200)
✅ 5/6: Page analysis completed successfully
✅ 6/6: Screenshot saved to /app/sap_screenshot.png
✅ COMPLETE: Test completed successfully in 8.67s

📊 TEST EXECUTION SUMMARY
✅ Overall Test Status: SUCCESS
✅ HTTP Response: 200 OK
📄 Page Title: SAP Fiori Launchpad
✅ Title Keywords: ['launchpad', 'sap']
✅ JavaScript Errors: 0
⚡ Performance:
   • Browser launch: 2.34s
   • Navigation: 3.12s
   • Total test time: 8.67s
📸 Screenshot: Saved to /app/sap_screenshot.png
```

## 📄 Format du rapport JSON

```json
{
  "test_info": {
    "url": "https://...",
    "timestamp": "2024-01-20T10:30:00Z",
    "browser": "chromium",
    "configuration": {
      "page_timeout": 60000,
      "screenshot_enabled": true
    }
  },
  "connectivity": {
    "status_code": 200,
    "status_text": "OK",
    "redirected": false,
    "ok": true
  },
  "page_info": {
    "title": "SAP Fiori Launchpad",
    "title_contains_expected": true,
    "title_matched_keywords": ["launchpad", "sap"],
    "content_length": 25847,
    "javascript_errors": [],
    "javascript_error_count": 0
  },
  "performance": {
    "browser_launch_time": 2.34,
    "navigation_time": 3.12,
    "total_test_time": 8.67
  },
  "progress": [
    {
      "step": "1/6",
      "message": "Playwright initialized successfully",
      "success": true,
      "timestamp": "2024-01-20T10:30:01Z"
    }
  ],
  "success": true,
  "errors": []
}
```

## 🔧 Script de monitoring corrigé

Le script `monitor-sap-playwright.sh` utilise **3 méthodes** pour trouver le dernier container :

1. **Tri par création** - `creationTime`
2. **Tri par nom** - Timestamp dans le nom
3. **Recherche manuelle** - Parcours de tous les containers

### Options disponibles
```bash
./monitor-sap-playwright.sh list              # Liste tous les containers
./monitor-sap-playwright.sh latest-logs       # Logs du dernier
./monitor-sap-playwright.sh latest-summary    # Résumé complet
./monitor-sap-playwright.sh monitor          # Temps réel
./monitor-sap-playwright.sh find-latest      # Nom du dernier container
```

### Menu interactif
```bash
./monitor-sap-playwright.sh

🎯 SAP Playwright Container Monitor Options:
1. List all SAP Playwright containers
2. Monitor latest container (real-time)
3. Get status of latest container
4. Get logs of latest container
5. Get complete test summary of latest container
[...]
```

## 🔍 Interprétation des résultats

### ✅ Test réussi
- Exit code: `0`
- Status HTTP: `200-299`
- Titre contient les mots-clés attendus
- Aucune erreur JavaScript critique
- Screenshot généré

### ❌ Test échoué
- Exit code: `1` ou autre
- Erreurs de connectivité/timeout
- Erreurs dans les logs détaillés
- Traceback Python disponible

### 🔍 Diagnostic approfondi
```bash
# Voir les logs détaillés du dernier test
./monitor-sap-playwright.sh latest-summary

# Surveiller un test en cours
./monitor-sap-playwright.sh monitor

# Trouver le nom exact du dernier container
./monitor-sap-playwright.sh find-latest
```

## 🚀 Redéploiement

```bash
# Nouveau test (nouveau container)
./deploy-sap-playwright.sh

# Le nom du container contient un timestamp unique
# Exemple: sap-playwright-tester-1753168234
```

## 💡 Avantages vs Projet 9999

| Aspect | 9999 | **10001** |
|--------|------|-----------|
| Méthode | HTTP requests | **Vrai navigateur Playwright** |
| Logs | Basiques | **Détaillés avec progress** |
| Screenshot | Non | **Full-page automatique** |
| JavaScript | Non testé | **Erreurs détectées** |
| Performance | Basique | **Métriques complètes** |
| Monitoring | Bugs de recherche | **Script corrigé, 3 méthodes** |
| Rapport | Simple | **JSON structuré complet** |

## 🔗 URLs utiles

- [SAP Launchpad testé](https://oneb-central-dev.launchpad.cfapps.eu10.hana.ondemand.com/site?siteId=1af3caee-8117-49e3-b787-7ad74914b5cb)
- [Playwright Documentation](https://playwright.dev/python/)
- [Azure Container Instances](https://docs.microsoft.com/azure/container-instances/)

---

**🎯 Projet 10001 - Solution complète pour tests SAP avec Playwright et monitoring avancé** 