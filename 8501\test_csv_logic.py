#!/usr/bin/env python3

import os
import csv
import tempfile
from datetime import datetime

def test_csv_append_logic():
    """Test CSV append logic simulation"""
    print("=" * 60)
    print("📊 CSV APPEND LOGIC TEST - Script 8501")
    print("=" * 60)
    
    # Create a temporary directory to simulate local results
    with tempfile.TemporaryDirectory() as temp_dir:
        csv_file_path = os.path.join(temp_dir, "8501_RTR.csv")
        
        # Simulate first run - create new CSV with header
        print("\n🔄 FIRST RUN - Creating new CSV")
        print("-" * 40)
        
        data1 = {
            "SCRIPT_ID": "8501",
            "SCRIPT_DATE": "03.06.2025",
            "SCRIPT_START_TIME": "09:30:00",
            "SCRIPT_END_TIME": "09:30:02",
            "STATION_ID": 200,
            "STATION_SITE": "AWS",
            "RESULT_MIN": 1250,
            "RESULT_MAX": 1250,
            "RESULT_AVG": 1250,
            "RESULT_ERR": 0,
            "RESPONSE_TIME": 1250
        }
        
        # Simulate: no existing file (first run)
        file_exists = os.path.exists(csv_file_path)
        print(f"File exists: {file_exists}")
        
        with open(csv_file_path, mode='a', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, fieldnames=data1.keys(), delimiter=';')
            
            if not file_exists:
                print("✅ Creating CSV header")
                writer.writeheader()
            
            writer.writerow(data1)
        
        print(f"✅ First entry written to: {csv_file_path}")
        
        # Show file content
        with open(csv_file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            print(f"File content after first run:\n{content}")
        
        # Simulate second run - append to existing CSV
        print("\n🔄 SECOND RUN - Appending to existing CSV")
        print("-" * 40)
        
        data2 = {
            "SCRIPT_ID": "8501",
            "SCRIPT_DATE": "03.06.2025",
            "SCRIPT_START_TIME": "09:35:00",
            "SCRIPT_END_TIME": "09:35:01",
            "STATION_ID": 200,
            "STATION_SITE": "AWS",
            "RESULT_MIN": 980,
            "RESULT_MAX": 980,
            "RESULT_AVG": 980,
            "RESULT_ERR": 0,
            "RESPONSE_TIME": 980
        }
        
        # Simulate: file exists (downloaded from Azure)
        file_exists = os.path.exists(csv_file_path)
        print(f"File exists: {file_exists}")
        
        with open(csv_file_path, mode='a', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, fieldnames=data2.keys(), delimiter=';')
            
            if not file_exists:
                print("Creating CSV header")
                writer.writeheader()
            else:
                print("✅ Appending to existing CSV (no header)")
            
            writer.writerow(data2)
        
        print(f"✅ Second entry appended to: {csv_file_path}")
        
        # Show final file content
        with open(csv_file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            print(f"Final file content:\n{content}")
        
        # Count lines
        lines = content.strip().split('\n')
        print(f"\n📊 Total lines: {len(lines)} (1 header + {len(lines)-1} data rows)")
        
        # Simulate third run - another append
        print("\n🔄 THIRD RUN - Another append")
        print("-" * 40)
        
        data3 = {
            "SCRIPT_ID": "8501",
            "SCRIPT_DATE": "03.06.2025",
            "SCRIPT_START_TIME": "09:40:00",
            "SCRIPT_END_TIME": "09:40:03",
            "STATION_ID": 200,
            "STATION_SITE": "AWS",
            "RESULT_MIN": -2000,
            "RESULT_MAX": -2000,
            "RESULT_AVG": -2000,
            "RESULT_ERR": 1,
            "RESPONSE_TIME": -1
        }
        
        with open(csv_file_path, mode='a', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, fieldnames=data3.keys(), delimiter=';')
            writer.writerow(data3)
        
        print(f"✅ Third entry appended")
        
        # Show final file content
        with open(csv_file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            print(f"Final file content:\n{content}")
        
        # Count lines
        lines = content.strip().split('\n')
        print(f"\n📊 Final total lines: {len(lines)} (1 header + {len(lines)-1} data rows)")
        
        print("\n✅ CSV append logic test completed successfully!")
        print("This simulates the Azure CSV workflow:")
        print("1. Download existing CSV from Azure (if exists)")
        print("2. Append new line to local file")
        print("3. Upload complete file back to Azure")

if __name__ == "__main__":
    test_csv_append_logic()
