# Use Python 3.11 with Playwright base image
FROM mcr.microsoft.com/playwright/python:v1.40.0-jammy

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    iputils-ping \
    dnsutils \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install Playwright browsers
RUN playwright install chromium

# Copy the Python script
COPY sap_playwright_tester.py .

# Create output directory
RUN mkdir -p /app/outputs

# Set executable permissions
RUN chmod +x sap_playwright_tester.py

# Verify files are in place
RUN ls -la /app/

# Default command
CMD ["python", "sap_playwright_tester.py"] 