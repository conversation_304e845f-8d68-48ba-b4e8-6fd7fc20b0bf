#!/usr/bin/env python3

import os
import pytz
from datetime import datetime

def detect_container_environment():
    """Detect if running in a container environment"""
    container_indicators = []
    
    try:
        # Docker environment file
        if os.path.exists('/.dockerenv'):
            container_indicators.append(True)
            print("🐳 Detected Docker environment file")
        
        # Check cgroup for docker/container
        if os.path.exists('/proc/1/cgroup'):
            with open('/proc/1/cgroup', 'r') as f:
                cgroup_content = f.read()
                if 'docker' in cgroup_content or 'container' in cgroup_content:
                    container_indicators.append(True)
                    print("🐳 Detected container in cgroup")
        
        # Kubernetes environment
        if os.environ.get('KUBERNETES_SERVICE_HOST'):
            container_indicators.append(True)
            print("🐳 Detected Kubernetes environment")
        
        # Generic container environment variables
        if os.environ.get('CONTAINER_NAME') or os.environ.get('DOCKER_CONTAINER'):
            container_indicators.append(True)
            print("🐳 Detected container environment variables")
            
        # Check TZ environment variable (set in our Dockerfile)
        if os.environ.get('TZ') == 'Europe/Berlin':
            container_indicators.append(True)
            print("🐳 Detected Berlin timezone environment variable")
        
        return any(container_indicators)
    except Exception as e:
        print(f"⚠️ Error detecting container environment: {e}")
        return False

def main():
    print("=" * 60)
    print("🕐 TIMEZONE DETECTION TEST - Script 8500")
    print("=" * 60)
    
    # Environment info
    print(f"TZ environment variable: {os.environ.get('TZ', 'Not set')}")
    print(f"Current working directory: {os.getcwd()}")
    
    # Container detection
    is_container = detect_container_environment()
    print(f"Container detected: {is_container}")
    
    # Timezone setup
    if is_container:
        timezone = pytz.timezone('Europe/Berlin')
        print("🐳 Running in container - using Europe/Berlin timezone")
    else:
        timezone = None
        print("💻 Running locally - using system local timezone")
    
    # Time tests
    print("\n" + "=" * 40)
    print("TIME TESTS")
    print("=" * 40)
    
    # Current time
    current_time = datetime.now()
    print(f"datetime.now(): {current_time}")
    print(f"Timezone info: {current_time.tzinfo}")
    
    if is_container and timezone:
        if current_time.tzinfo is None:
            localized_time = timezone.localize(current_time)
            print(f"Localized to Berlin: {localized_time}")
        else:
            converted_time = current_time.astimezone(timezone)
            print(f"Converted to Berlin: {converted_time}")
    
    # ISO format
    if is_container and timezone and current_time.tzinfo is None:
        final_time = timezone.localize(current_time)
    else:
        final_time = current_time
        
    print(f"Final timestamp: {final_time}")
    print(f"ISO format: {final_time.isoformat()}")
    print(f"Date: {final_time.strftime('%d.%m.%Y')}")
    print(f"Time: {final_time.strftime('%H:%M:%S')}")

if __name__ == "__main__":
    main()
