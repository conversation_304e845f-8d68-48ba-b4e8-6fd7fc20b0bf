location: "Germany West Central"
identity:
  type: UserAssigned
  userAssignedIdentities:
    /subscriptions/f8d8859e-e3f9-4c7f-a99b-c91d19865003/resourceGroups/RG-EDGMON-RUN/providers/Microsoft.ManagedIdentity/userAssignedIdentities/scriptmonregistry2025-identity: {}
properties:
  environmentId: /subscriptions/f8d8859e-e3f9-4c7f-a99b-c91d19865003/resourceGroups/RG-EDGMON-RUN/providers/Microsoft.App/managedEnvironments/managedEnvironment-RGEDGMONRUN-b130
  configuration:
    scheduleTriggerConfig:
      cronExpression: "*/5 * * * *"  # Every 5 minutes
      parallelism: 1
      replicaCompletionCount: 1
    registries:
    - server: scriptmonregistry2025.azurecr.io
      identity: 3b481759-f184-408c-ac79-dc3d915606d5
    replicaTimeout: 3600
    replicaRetryLimit: 2
    triggerType: Schedule
  template:
    containers:
    - image: scriptmonregistry2025.azurecr.io/eon-monitoring-3111:latest
      name: eon-monitoring
      env:
      - name: AZURE_CLIENT_ID
        value: 3b481759-f184-408c-ac79-dc3d915606d5
      resources:
        cpu: 1.0
        memory: 2.0Gi
      volumeMounts:
      - volumeName: azure-files-volume
        mountPath: /mnt/monitoring
    volumes:
    - name: azure-files-volume
      storageType: AzureFile
      storageName: script-monitoring-storage
