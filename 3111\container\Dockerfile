# Utiliser une image Python avec support pour Playwright
FROM mcr.microsoft.com/playwright/python:v1.40.0-jammy

# Définir le répertoire de travail
WORKDIR /app

# Installer des dépendances système supplémentaires
RUN apt-get update && apt-get install -y \
    curl \
    iputils-ping \
    net-tools \
    dnsutils \
    unixodbc-dev \
    freetds-dev \
    freetds-bin \
    tdsodbc \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Installer les pilotes ODBC pour SQL Server
RUN curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add - \
    && curl https://packages.microsoft.com/config/ubuntu/22.04/prod.list > /etc/apt/sources.list.d/mssql-release.list \
    && apt-get update \
    && ACCEPT_EULA=Y apt-get install -y msodbcsql18 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copier les fichiers de requirements
COPY requirements.txt .

# Installer les dépendances Python
RUN pip install --no-cache-dir -r requirements.txt

# Installer les navigateurs Playwright
RUN playwright install chromium

# Copier le script Python
COPY eonde_test_azure.py .

# Créer les répertoires nécessaires
RUN mkdir -p /mnt/monitoring/results /mnt/monitoring/img /tmp/monitoring/results /tmp/monitoring/img

# Définir les permissions
RUN chmod -R 777 /mnt/monitoring /tmp/monitoring

# Définir les variables d'environnement
ENV AZURE_CLIENT_ID=""
ENV PYTHONUNBUFFERED=1
ENV PLAYWRIGHT_BROWSERS_PATH=/ms-playwright

# Commande par défaut
CMD ["python", "-u", "eonde_test_azure.py"]
