#!/bin/bash

#############################################################
#                USER CONFIGURATION SECTION                 #
#############################################################
# Modify these values according to your Azure environment

# Script Identifier (used for naming resources and secrets)
SCRIPT="8501"

# Azure Resource Group and Location
RESOURCE_GROUP="RG-EDGMON-RUN"
LOCATION="Germany West Central"

# Azure Services Names
KEYVAULT_NAME="scriptsmonitoring2025"
ACR_NAME="scriptmonregistry2025"
STORAGE_ACCOUNT="scriptsmonitoring2025"
MANAGED_IDENTITY="scriptmonregistry2025-identity"
CONTAINER_APP_ENV="managedEnvironment-RGEDGMONRUN-b130"

# Application Configuration
CONTAINER_APP_NAME="powercloud-monitoring-${SCRIPT}-app"
IMAGE_NAME="powercloud-monitoring-${SCRIPT}"
IMAGE_TAG="latest"

# Powercloud API Configuration (will be stored in Key Vault)
POWERCLOUD_AUTH_HASH="ebf7e5994adefcaa0aa2d8b80a562d39"
POWERCLOUD_AUTH_HEADER="Basic Y2xpZW50I2VvbjpKLExMe19CNCI3cnIiLFt1"
POWERCLOUD_CUSTOMER_ID="*********"

# Database Configuration (will be stored in Key Vault)
DB_SERVER="b2smepcw-sql-12345.database.windows.net"
DB_NAME="EON_MON_DB"
DB_USERNAME="sa1bmon"
DB_PASSWORD="Affe1212"

# Container Resources
CPU="0.5"
MEMORY="1.0Gi"
MIN_REPLICAS="0"
MAX_REPLICAS="1"

#############################################################
#                 DEPLOYMENT SCRIPT LOGIC                   #
#############################################################
# Do not modify below this line unless you know what you're doing

echo "🚀 Starting deployment of Powercloud Monitoring Application"
echo "=========================================================="

# 1. Verify Azure CLI is installed and logged in
echo "📋 Checking Azure CLI login..."
az account show > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ Please login to Azure with 'az login'"
    exit 1
fi

# Get the Managed Identity Client ID
echo "🔑 Getting Managed Identity Client ID..."
IDENTITY_ID=$(az identity show --name $MANAGED_IDENTITY --resource-group $RESOURCE_GROUP --query id -o tsv)
CLIENT_ID=$(az identity show --name $MANAGED_IDENTITY --resource-group $RESOURCE_GROUP --query clientId -o tsv)

if [ -z "$IDENTITY_ID" ] || [ -z "$CLIENT_ID" ]; then
    echo "❌ Failed to get Managed Identity details"
    exit 1
fi

echo "✅ Using Managed Identity: $MANAGED_IDENTITY (Client ID: $CLIENT_ID)"

# 2. Build and push Docker image to ACR
echo "🔨 Building and pushing Docker image..."
az acr login --name $ACR_NAME

# Build the image
docker build -t $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG .

# Push to ACR
docker push $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG

echo "✅ Docker image pushed to ACR"

# 3. Add secrets to Key Vault (if not already done)
echo "🔐 Configuring secrets in Key Vault..."

# Secrets pour Powercloud
az keyvault secret set --vault-name $KEYVAULT_NAME --name "${SCRIPT}-powercloud-auth-hash" --value "ebf7e5994adefcaa0aa2d8b80a562d39" --output none
az keyvault secret set --vault-name $KEYVAULT_NAME --name "${SCRIPT}-powercloud-auth-header" --value "Basic Y2xpZW50I2VvbjpKLExMe19CNCI3cnIiLFt1" --output none
az keyvault secret set --vault-name $KEYVAULT_NAME --name "${SCRIPT}-powercloud-customer-id" --value "*********" --output none

# Secrets pour Splunk
az keyvault secret set --vault-name $KEYVAULT_NAME --name "${SCRIPT}-splunk-hec-url" --value "https://splunk-hec.eon.com:443/services/collector/event" --output none
az keyvault secret set --vault-name $KEYVAULT_NAME --name "${SCRIPT}-splunk-token" --value "bbab61ff-f874-4e71-8403-257370da6485" --output none

# Secrets pour New Relic
az keyvault secret set --vault-name $KEYVAULT_NAME --name "${SCRIPT}-new-relic-api-key" --value "eu01xx0a982ff080c0b184b6137750a8FFFFNRAL" --output none

# Secrets pour Database
az keyvault secret set --vault-name $KEYVAULT_NAME --name "${SCRIPT}-db-server" --value "$DB_SERVER" --output none
az keyvault secret set --vault-name $KEYVAULT_NAME --name "${SCRIPT}-db-name" --value "$DB_NAME" --output none
az keyvault secret set --vault-name $KEYVAULT_NAME --name "${SCRIPT}-db-username" --value "$DB_USERNAME" --output none
az keyvault secret set --vault-name $KEYVAULT_NAME --name "${SCRIPT}-db-password" --value "$DB_PASSWORD" --output none

echo "✅ Secrets configured in Key Vault"

# 4. Create or update Container App
echo "🌐 Deploying Container App..."

# Check if app already exists
if az containerapp show --name $CONTAINER_APP_NAME --resource-group $RESOURCE_GROUP > /dev/null 2>&1; then
    echo "🔄 Updating existing Container App..."
    az containerapp update \
        --name $CONTAINER_APP_NAME \
        --resource-group $RESOURCE_GROUP \
        --image $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG
else
    echo "🆕 Creating new Container App..."
    az containerapp create \
        --name $CONTAINER_APP_NAME \
        --resource-group $RESOURCE_GROUP \
        --environment $CONTAINER_APP_ENV \
        --image $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG \
        --user-assigned $IDENTITY_ID \
        --registry-server $ACR_NAME.azurecr.io \
        --registry-identity $IDENTITY_ID \
        --env-vars AZURE_CLIENT_ID=$CLIENT_ID SCRIPT_ID=$SCRIPT \
        --cpu $CPU \
        --memory $MEMORY \
        --min-replicas $MIN_REPLICAS \
        --max-replicas $MAX_REPLICAS
fi

if [ $? -ne 0 ]; then
    echo "❌ Failed to deploy Container App"
    exit 1
fi

echo "✅ Container App deployed successfully"
echo ""
echo "🎉 Deployment completed successfully!"
echo "You can now create a scheduled job with 'create-scheduled-job.sh'"






