#!/bin/bash

#############################################################
#                USER CONFIGURATION SECTION                 #
#############################################################
# Modify these values according to your Azure environment

# Script Identifier (used for naming resources and secrets)
SCRIPT="3110"

# Azure Resource Group and Location
RESOURCE_GROUP="RG-EDGMON-RUN"
LOCATION="Germany West Central"

# Azure Services Names
KEYVAULT_NAME="scriptsmonitoring2025"
ACR_NAME="scriptmonregistry2025"
STORAGE_ACCOUNT="scriptsmonitoring2025"
MANAGED_IDENTITY="scriptmonregistry2025-identity"
CONTAINER_APP_ENV="managedEnvironment-RGEDGMONRUN-b130"

# Application Configuration
CONTAINER_APP_NAME="eon-monitoring-${SCRIPT}-app"
IMAGE_NAME="eon-monitoring-${SCRIPT}"
IMAGE_TAG="latest"

# EON Credentials (will be stored in Key Vault)
EON_EMAIL="<EMAIL>"
EON_PASSWORD="Stockholm_2021"

# Database Configuration (will be stored in Key Vault)
DB_SERVER="b2smepcw-sql-12345.database.windows.net"
DB_NAME="EON_MON_DB"
DB_USERNAME="sa1bmon"
DB_PASSWORD="Affe1212"

# Container Resources
CPU="1.0"
MEMORY="2.0Gi"
MIN_REPLICAS="0"
MAX_REPLICAS="1"

#############################################################
#                 DEPLOYMENT SCRIPT LOGIC                   #
#############################################################
# Do not modify below this line unless you know what you're doing

echo "🚀 Deploying EON Monitoring application ${SCRIPT} on Azure Container Apps"

# 1. Connect to Azure (if necessary)
echo "📋 Checking Azure connection..."
az account show > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ Please connect to Azure with 'az login'"
    exit 1
fi

# 2. Build and push Docker image to ACR
echo "🔨 Building and pushing Docker image..."
az acr login --name $ACR_NAME

# Build the image
docker build -t $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG .

# Push to ACR
docker push $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG

echo "✅ Docker image pushed to ACR"

# 3. Add secrets to Key Vault (if not already done)
echo "🔐 Configuring secrets in Key Vault..."

# Secrets for EON
az keyvault secret set --vault-name $KEYVAULT_NAME --name "${SCRIPT}-eon-email" --value "$EON_EMAIL" --output none
az keyvault secret set --vault-name $KEYVAULT_NAME --name "${SCRIPT}-eon-password" --value "$EON_PASSWORD" --output none

# Secrets for the database
az keyvault secret set --vault-name $KEYVAULT_NAME --name "${SCRIPT}-db-server" --value "$DB_SERVER" --output none
az keyvault secret set --vault-name $KEYVAULT_NAME --name "${SCRIPT}-db-name" --value "$DB_NAME" --output none
az keyvault secret set --vault-name $KEYVAULT_NAME --name "${SCRIPT}-db-username" --value "$DB_USERNAME" --output none
az keyvault secret set --vault-name $KEYVAULT_NAME --name "${SCRIPT}-db-password" --value "$DB_PASSWORD" --output none

echo "✅ Secrets configured in Key Vault with prefix '${SCRIPT}-'"

# 4. Get Managed Identity ID
echo "🆔 Retrieving Managed Identity ID..."
IDENTITY_ID=$(az identity show --resource-group $RESOURCE_GROUP --name $MANAGED_IDENTITY --query id --output tsv)
CLIENT_ID=$(az identity show --resource-group $RESOURCE_GROUP --name $MANAGED_IDENTITY --query clientId --output tsv)

# 5. Grant permissions to Managed Identity
echo "🔒 Configuring permissions..."

# Key Vault permissions
az keyvault set-policy --name $KEYVAULT_NAME --object-id $(az identity show --resource-group $RESOURCE_GROUP --name $MANAGED_IDENTITY --query principalId --output tsv) --secret-permissions get list

# Storage Account permissions
az role assignment create --assignee $CLIENT_ID --role "Storage File Data SMB Share Contributor" --scope "/subscriptions/$(az account show --query id --output tsv)/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.Storage/storageAccounts/$STORAGE_ACCOUNT"

echo "✅ Permissions configured"

# 6. Create or update Container App
echo "📦 Deploying Container App..."

# Check if app already exists
if az containerapp show --name $CONTAINER_APP_NAME --resource-group $RESOURCE_GROUP > /dev/null 2>&1; then
    echo "🔄 Updating existing Container App..."
    az containerapp update \
        --name $CONTAINER_APP_NAME \
        --resource-group $RESOURCE_GROUP \
        --image $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG
else
    echo "🆕 Creating new Container App..."
    az containerapp create \
        --name $CONTAINER_APP_NAME \
        --resource-group $RESOURCE_GROUP \
        --environment $CONTAINER_APP_ENV \
        --image $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG \
        --user-assigned $IDENTITY_ID \
        --registry-server $ACR_NAME.azurecr.io \
        --registry-identity $IDENTITY_ID \
        --env-vars AZURE_CLIENT_ID=$CLIENT_ID SCRIPT_ID=$SCRIPT \
        --cpu $CPU \
        --memory $MEMORY \
        --min-replicas $MIN_REPLICAS \
        --max-replicas $MAX_REPLICAS
fi

echo "✅ Container App deployed"

# 7. Schedule configuration (if not already done)
echo "⏰ Note: To configure periodic execution, you need to:"
echo "1. Use Azure Container Apps Jobs with a cron schedule"
echo "2. Or use Azure Logic Apps to trigger the application"
echo "3. Or use Azure Functions with a timer trigger"

echo ""
echo "🎉 Deployment completed for script ${SCRIPT}!"
echo "📊 Monitor logs with: az containerapp logs show --name $CONTAINER_APP_NAME --resource-group $RESOURCE_GROUP --follow"



