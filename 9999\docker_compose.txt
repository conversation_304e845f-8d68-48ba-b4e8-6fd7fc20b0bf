version: '3.8'

services:
  web-tester:
    build: .
    environment:
      - TEST_URL=${TEST_URL:-https://www.google.com}
      - TIMEOUT=${TIMEOUT:-30}
      - OUTPUT_FILE=/app/results/test_results.json
    volumes:
      - ./results:/app/results
    networks:
      - web-test-network
    restart: unless-stopped

  # Service optionnel pour une API web
  web-tester-api:
    build: .
    command: ["python", "api_server.py"]
    ports:
      - "8000:8000"
    environment:
      - DEFAULT_URL=${TEST_URL:-https://www.google.com}
    networks:
      - web-test-network
    restart: unless-stopped

networks:
  web-test-network:
    driver: bridge

volumes:
  results: