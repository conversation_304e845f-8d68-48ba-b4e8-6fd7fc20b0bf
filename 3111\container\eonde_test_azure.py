from playwright.sync_api import <PERSON><PERSON>, sync_playwright, expect
import os
import time
from datetime import date
import datetime as dt
from datetime import datetime
import pymssql
import uuid
from azure.keyvault.secrets import SecretClient
from azure.identity import DefaultAzureCredential
from azure.storage.fileshare import ShareFileClient
import tempfile
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AzureEonMonitoring:
    def __init__(self):
        # Configuration Azure
        self.keyvault_name = "scriptsmonitoring2025"
        self.keyvault_url = f"https://{self.keyvault_name}.vault.azure.net/"
        self.storage_account_name = "scriptsmonitoring2025"
        self.file_share_name = "script-monitoring"
        self.container_name = "3111"
        
        # Initialisation des clients Azure
        self.credential = DefaultAzureCredential()
        self.secret_client = SecretClient(vault_url=self.keyvault_url, credential=self.credential)
        
        # Chemins locaux
        self.local_results_path = "/mnt/monitoring/results"
        self.local_img_path = "/mnt/monitoring/img"
        
        # Créer les répertoires locaux
        os.makedirs(self.local_results_path, exist_ok=True)
        os.makedirs(self.local_img_path, exist_ok=True)
        
        logger.info("Azure Eon Monitoring initialized")

    def get_secret(self, secret_name):
        """Récupère un secret depuis Azure Key Vault"""
        try:
            retrieved_secret = self.secret_client.get_secret(secret_name)
            return retrieved_secret.value
        except Exception as e:
            logger.error(f"Erreur lors de la récupération du secret {secret_name}: {str(e)}")
            return None

    def upload_to_azure_storage(self, local_file_path, remote_file_path):
        """Upload un fichier vers Azure File Share"""
        try:
            file_client = ShareFileClient(
                account_url=f"https://{self.storage_account_name}.file.core.windows.net",
                share_name=self.file_share_name,
                file_path=remote_file_path,
                credential=self.credential
            )
            
            with open(local_file_path, "rb") as data:
                file_client.upload_file(data, overwrite=True)
            
            logger.info(f"Fichier uploadé: {remote_file_path}")
            return True
        except Exception as e:
            logger.error(f"Erreur lors de l'upload du fichier {local_file_path}: {str(e)}")
            return False

    def log(self, sLine):
        """Log les résultats localement et sur Azure"""
        try:
            # Créer le fichier local
            local_file_path = os.path.join(self.local_results_path, "3111_RTR.csv")
            
            # Vérifier si le fichier existe, sinon créer le header
            if not os.path.exists(local_file_path):
                with open(local_file_path, 'w') as datei:
                    sHeader = "SCRIPT_ID;SCRIPT_DATE;SCRIPT_START_TIME;LOAD_LOGIN_PAGE;LOGIN;METER_READING;ABSCHLAG;LOGOUT;\n"
                    datei.write(sHeader)

            # Ajouter la ligne de données
            with open(local_file_path, 'a') as datei:
                formatted_line = "3111;" + date.today().strftime("%d.%m.%Y") + ";" + \
                    datetime.now().strftime("%H:%M:%S") + ";" + sLine + "\n"
                datei.write(formatted_line)

            # Upload vers Azure File Share
            remote_path = f"{self.container_name}/3111_RTR.csv"
            self.upload_to_azure_storage(local_file_path, remote_path)
            
            logger.info(f"Log enregistré: {sLine}")
            
        except Exception as e:
            logger.error(f"Erreur lors de l'enregistrement du log: {str(e)}")

    def logdb(self, sLoginValue):
        """Log vers Azure SQL Database"""
        try:
            print("=== DB LOGGING START ===")
            
            # Récupérer les credentials de la DB depuis Key Vault
            db_server = self.get_secret("db-server")
            print(f"DB Server from KeyVault: {db_server}")
            if not db_server:
                db_server = "b2smepcw-sql-12345.database.windows.net"
                print(f"Using default DB Server: {db_server}")
                
            db_name = self.get_secret("db-name")
            print(f"DB Name from KeyVault: {db_name}")
            if not db_name:
                db_name = "EON_MON_DB"
                print(f"Using default DB Name: {db_name}")
                
            db_username = self.get_secret("db-username")
            print(f"DB Username from KeyVault: {db_username}")
            if not db_username:
                db_username = "sa1bmon"
                print(f"Using default DB Username: {db_username}")
                
            db_password = self.get_secret("db-password")
            print(f"DB Password retrieved: {'Yes' if db_password else 'No'}")
            if not db_password:
                db_password = "Affe1212"
                print("Using default DB Password")
            
            # Connection string pour Azure SQL Database
            conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={db_server};DATABASE={db_name};UID={db_username};PWD={db_password};Encrypt=yes;TrustServerCertificate=no;Connection Timeout=30;'
            masked_conn_str = conn_str.replace(db_password, "********")
            print(f"Connection string: {masked_conn_str}")
            
            print("Attempting to connect to database...")
            conn = pyodbc.connect(conn_str)
            print("✅ Database connection successful")
            
            cursor = conn.cursor()
            print("✅ Cursor created")

            MID = str(uuid.uuid4())
            TIMESTAMP = dt.datetime.now().strftime("%Y-%m-%d %H:%M:%S") + ".000"
            print(f"Generated MID: {MID}")
            print(f"Generated TIMESTAMP: {TIMESTAMP}")

            # Insérer dans la table Measurement
            sql_measurement = "INSERT INTO Measurement (MID,SCRIPTID,TIMESTAMP,STATIONID) VALUES (?,?,?,?)"
            print(f"Executing SQL: {sql_measurement}")
            print(f"With values: MID={MID}, SCRIPTID=3111, TIMESTAMP={TIMESTAMP}, STATIONID=200")
            
            cursor.execute(sql_measurement, (MID, '3111', TIMESTAMP, 200))
            print("✅ Measurement record inserted")
            
            # Insérer dans la table RESULT
            sql_result = "INSERT INTO RESULT (MID,NAME,VALUE) VALUES (?,?,?)"
            print(f"Executing SQL: {sql_result}")
            print(f"With values: MID={MID}, NAME=LOGIN, VALUE={sLoginValue}")
            
            cursor.execute(sql_result, (MID, 'LOGIN', sLoginValue))
            print("✅ Result record inserted")
            
            print("Committing transaction...")
            conn.commit()
            print("✅ Transaction committed")
            
            cursor.close()
            conn.close()
            print("✅ Database connection closed")
            
            print(f"✅ DB LOGGING COMPLETE - Login time {sLoginValue}ms recorded")
            print("=== DB LOGGING END ===")
            
        except Exception as e:
            print("=== DB LOGGING ERROR ===")
            print(f"❌ Error: {str(e)}")
            
            # Afficher des informations détaillées sur l'erreur
            import traceback
            print(f"❌ Stack trace:")
            print(traceback.format_exc())
            
            # Vérifier si c'est un problème de connexion
            if "connect" in str(e).lower():
                print("❌ Connection error detected. Possible causes:")
                print("  - Database server is not accessible")
                print("  - Firewall rules blocking connection")
                print("  - Incorrect server name or credentials")
                print("  - ODBC driver not installed correctly")
            
            # Vérifier si c'est un problème d'authentification
            elif "login" in str(e).lower() or "password" in str(e).lower() or "authentication" in str(e).lower():
                print("❌ Authentication error detected. Possible causes:")
                print("  - Incorrect username or password")
                print("  - User does not have access to the database")
                print("  - Account locked or disabled")
            
            # Vérifier si c'est un problème de requête SQL
            elif "syntax" in str(e).lower() or "invalid" in str(e).lower():
                print("❌ SQL syntax error detected. Possible causes:")
                print("  - Table does not exist")
                print("  - Column names are incorrect")
                print("  - SQL syntax error in query")
            
            print("=== DB LOGGING ERROR END ===")

    def GetMS(self, fTime):
        """Convertit le temps en millisecondes"""
        if fTime <= 0:
            return "0"
        m1 = int(round(fTime * 1000, 0))
        return str(m1)

    def take_screenshot(self, page, step_name):
        """Prend un screenshot et l'upload vers Azure"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
            filename = f"3111_{timestamp}_{step_name}.png"
            local_path = os.path.join(self.local_img_path, filename)
            
            page.screenshot(path=local_path)
            
            # Upload vers Azure
            remote_path = f"{self.container_name}/screenshots/{filename}"
            self.upload_to_azure_storage(local_path, remote_path)
            
            return local_path
            
        except Exception as e:
            logger.error(f"Erreur lors de la capture d'écran: {str(e)}")
            return None

    def run(self, playwright: Playwright) -> None:
        """Fonction principale d'exécution du monitoring"""
        logger.info("Démarrage du monitoring EON")
        
        # Récupérer les credentials depuis Key Vault
        email = self.get_secret("eon-email")
        password = self.get_secret("eon-password")
        
        if not email or not password:
            logger.error("Impossible de récupérer les credentials depuis Key Vault")
            return

        browser = playwright.chromium.launch(headless=True)  # Headless pour Azure
        context = browser.new_context(user_agent="<EMAIL>?9185")
        page = context.new_page()
        
        bLoginPageError = False
        bContinue = True
        
        # Métriques de performance
        metrics = {
            'load_login_page': 0,
            'login': 0,
            'meter_reading': 0,
            'abschlag': 0,
            'logout': 0
        }

        ############################################################################
        # Load Login Page
        ############################################################################
        logger.info("Étape 1: Chargement de la page de connexion")
        start = time.time()

        try:
            page.goto("https://da.eon.de/s/login/")
            page.wait_for_url("https://da.eon.de/s/login/")
            
            try:
                page.get_by_test_id("uc-accept-all-button").click(timeout=3000)
            except:
                pass
            
            page.wait_for_load_state("load")
            page.get_by_role("textbox", name="E-Mail-Adresse").click()
            page.get_by_role("textbox", name="E-Mail-Adresse").fill(email)
            page.get_by_role("textbox", name="Passwort").click()
            page.get_by_role("textbox", name="Passwort").fill(password)
            
            stop = time.time()
            metrics['load_login_page'] = stop - start
            logger.info(f"Page de connexion chargée en {self.GetMS(metrics['load_login_page'])}ms")

        except Exception as e:
            logger.error(f"Erreur lors du chargement de la page de connexion: {str(e)}")
            metrics['load_login_page'] = 0
            bContinue = False
            bLoginPageError = True
            self.take_screenshot(page, "LOAD_LOGIN_PAGE_ERROR")

        ############################################################################
        # Perform Login
        ############################################################################
        if bContinue:
            logger.info("Étape 2: Connexion")
            start = time.time()
            
            try:
                page.get_by_role("button", name="Anmelden").click()
                page.wait_for_url("https://www.eon.de/de/meineonda/meine-uebersicht.html")
                page.wait_for_load_state("load")

                try:
                    page.get_by_text("Ablehnen").click(timeout=5000)
                except:
                    pass

                page.locator("login-dashboard-app").get_by_text("Rechnung").first.click(timeout=30000)
                
                stop = time.time()
                metrics['login'] = stop - start
                logger.info(f"Connexion réussie en {self.GetMS(metrics['login'])}ms")

            except Exception as e:
                logger.error(f"Erreur lors de la connexion: {str(e)}")
                metrics['login'] = 0
                bContinue = False
                screenshot_path = self.take_screenshot(page, "LOGIN_ERROR")
                
                # Vérifier si c'est une erreur spécifique (taille de screenshot)
                if screenshot_path and os.path.exists(screenshot_path):
                    file_size = os.path.getsize(screenshot_path)
                    if 385000 < file_size < 386000:
                        logger.warning("Erreur de connexion détectée - Arrêt du script")
                        return

        ############################################################################
        # Test Zählerstand (Meter Reading)
        ############################################################################
        if bContinue:
            logger.info("Étape 3: Test du relevé de compteur")
            start = time.time()
            
            try:
                page.goto("https://www.eon.de/de/meineonda/mein-zaehlerstand.html")
                page.wait_for_url("https://www.eon.de/de/meineonda/mein-zaehlerstand.html")
                page.locator("login-meter-read-submission-app").get_by_text("Zählernummer: 9701696").click(timeout=55000)
                
                stop = time.time()
                metrics['meter_reading'] = stop - start
                logger.info(f"Test relevé compteur réussi en {self.GetMS(metrics['meter_reading'])}ms")

            except Exception as e:
                logger.error(f"Erreur lors du test du relevé de compteur: {str(e)}")
                metrics['meter_reading'] = 0

        ############################################################################
        # Test Abschlag (Installment)
        ############################################################################
        if bContinue:
            logger.info("Étape 4: Test des acomptes")
            start = time.time()
            
            try:
                page.goto("https://www.eon.de/de/meineonda/mein-abschlag.html")
                page.get_by_text("Abschlagseingabe").click(timeout=55000)
                
                stop = time.time()
                metrics['abschlag'] = stop - start
                logger.info(f"Test acomptes réussi en {self.GetMS(metrics['abschlag'])}ms")

            except Exception as e:
                logger.error(f"Erreur lors du test des acomptes: {str(e)}")
                metrics['abschlag'] = 0

        ############################################################################
        # Logout
        ############################################################################
        logger.info("Étape 5: Déconnexion")
        start = time.time()
        
        try:
            page.get_by_role("menuitem", name="Navigiere zu https://www.eon.de/de/pk.html").locator("svg").click()
            page.get_by_text("Sie haben sich erfolgreich abgemeldet.").click(timeout=5000)
            
            stop = time.time()
            metrics['logout'] = stop - start
            logger.info(f"Déconnexion réussie en {self.GetMS(metrics['logout'])}ms")

        except Exception as e:
            logger.error(f"Erreur lors de la déconnexion: {str(e)}")
            metrics['logout'] = 0

        # Fermeture du navigateur
        context.close()
        browser.close()

        # Enregistrement des résultats
        if not bLoginPageError:
            log_line = f"{self.GetMS(metrics['load_login_page'])};{self.GetMS(metrics['login'])};{self.GetMS(metrics['meter_reading'])};{self.GetMS(metrics['abschlag'])};{self.GetMS(metrics['logout'])};"
            self.log(log_line)
            
            # Log en DB uniquement le temps de login
            try:
                self.logdb(self.GetMS(metrics['login']))
            except Exception as e:
                logger.error(f"Erreur lors de l'enregistrement en DB: {str(e)}")

        logger.info("Monitoring EON terminé")

def main():
    """Point d'entrée principal"""
    monitoring = AzureEonMonitoring()
    
    with sync_playwright() as playwright:
        monitoring.run(playwright)

if __name__ == "__main__":
    main()
