# EON Monitoring Deployment Guide on Azure Container Apps

## 📋 Prerequisites

Your existing Azure infrastructure:
- ✅ Resource Group: `RG-EDGMON-RUN`
- ✅ Key Vault: `scriptsmonitoring2025`
- ✅ Container Registry: `SCRIPTMONREGISTRY2025`
- ✅ Storage Account: `scriptsmonitoring2025`
- ✅ Managed Identity: `scriptmonregistry2025-identity`
- ✅ Container Apps Environment: `managedEnvironment-RGEDGMONRUN-b130`

## 🚀 Deployment Steps

### 1. File Preparation
Create a project folder and copy the following files:
- `Dockerfile`
- `requirements.txt`
- `eonde_test_azure.py`

### 2. Initial Deployment
```bash
# Make script executable
chmod +x deploy.sh

# Run deployment
./deploy.sh
```

### 3. Scheduled Job Configuration
```bash
# Create job with automatic scheduling
chmod +x create-scheduled-job.sh
./create-scheduled-job.sh
```

## 🔧 Configuration

### Key Vault Secrets
The following secrets will be automatically created:
- `eon-email`: `<EMAIL>`
- `eon-password`: `@Test@Ocins04@`
- `db-server`: `b2smepcw-sql-12345.database.windows.net`
- `db-name`: `EON_MON_DB`
- `db-username`: `sa1bmon`
- `db-password`: `Affe1212`

### Default Schedule
- **Frequency**: Every 30 minutes
- **Cron expression**: `0 */30 * * * *`
- **Timeout**: 1 hour per execution
- **Retry**: 2 attempts on failure

## 📊 Monitoring and Maintenance

### Using the monitoring script
```bash
chmod +x monitoring-commands.sh
./monitoring-commands.sh
```

### Useful manual commands

#### View job status
```bash
az containerapp job show --name eon-monitoring-job --resource-group RG-EDGMON-RUN
```

#### List executions
```bash
az containerapp job execution list --name eon-monitoring-job --resource-group RG-EDGMON-RUN
```

#### View execution logs
```bash
az containerapp job logs show --name eon-monitoring-job --resource-group RG-EDGMON-RUN --execution-name EXECUTION_NAME
```

#### Start manually
```bash
az containerapp job start --name eon-monitoring-job --resource-group RG-EDGMON-RUN
```

## 📁 Data Structure

### Azure File Share
```
script-monitoring/
└── 3111/
    ├── 3111_RTR.csv          # Performance logs
    └── screenshots/          # Error screenshots
        ├── 3111_YYYYMMDD-HHMMSS_LOGIN_ERROR.png
        └── 3111_YYYYMMDD-HHMMSS_LOAD_LOGIN_PAGE_ERROR.png
```

### Azure SQL Database
- **Server**: `b2smepcw-sql-12345.database.windows.net`
- **Database**: `EON_MON_DB`
- **Tables**:
  - `Measurement`: Main measurements
  - `RESULT`: Detailed results

## 🔄 Schedule Modifications

### Cron schedule examples
```bash
# Every 15 minutes
az containerapp job update --name eon-monitoring-job --resource-group RG-EDGMON-RUN --cron-expression "0 */15 * * * *"

# Every hour
az containerapp job update --name eon-monitoring-job --resource-group RG-EDGMON-RUN --cron-expression "0 0 * * * *"

# Daily at 8:00 AM
az containerapp job update --name eon-monitoring-job --resource-group RG-EDGMON-RUN --cron-expression "0 0 8 * * *"

# Monday to Friday at 9:00 AM
az containerapp job update --name eon-monitoring-job --resource-group RG-EDGMON-RUN --cron-expression "0 0 9 * * 1-5"
```

## 🔍 Troubleshooting

### Check permissions
```bash
# Verify Key Vault access
az keyvault secret show --vault-name scriptsmonitoring2025 --name eon-email

# Verify Storage access
az storage file list --account-name scriptsmonitoring2025 --share-name script-monitoring
```

### Debug logs
```bash
# Real-time logs
az containerapp job logs show --name eon-monitoring-job --resource-group RG-EDGMON-RUN --follow

# Specific execution logs
az containerapp job logs show --name eon-monitoring-job --resource-group RG-EDGMON-RUN --execution-name EXECUTION_NAME
```

### Redeploy new version
```bash
# Rebuild and push image
az acr login --name SCRIPTMONREGISTRY2025
docker build -t SCRIPTMONREGISTRY2025.azurecr.io/eon-monitoring:latest .
docker push SCRIPTMONREGISTRY2025.azurecr.io/eon-monitoring:latest

# Force redeployment
az containerapp job update --name eon-monitoring-job --resource-group RG-EDGMON-RUN --image SCRIPTMONREGISTRY2025.azurecr.io/eon-monitoring:latest
```

## 📈 Collected Metrics

The script collects the following metrics (in milliseconds):
- **LOAD_LOGIN_PAGE**: Login page loading time
- **LOGIN**: Login and authentication time
- **METER_READING**: Meter reading page access time
- **ABSCHLAG**: Installment page access time
- **LOGOUT**: Logout time

## 🚨 Alerts and Notifications

To configure alerts on failure:
1. Use Azure Monitor to monitor Container Apps Jobs
2. Configure alerts based on execution status
3. Integrate with Azure Logic Apps for notifications

## 🔒 Security

- ✅ Managed Identity usage for authentication
- ✅ Secrets stored in Azure Key Vault
- ✅ Encrypted communications (TLS/SSL)
- ✅ Restricted network access via Container Apps environment
- ✅ Docker images in private registry (ACR)

## 💡 Performance Optimization

### Resource Allocation
- **CPU**: 1.0 core (adjustable based on load)
- **Memory**: 2.0 GB (sufficient for Playwright + Chromium)
- **Timeout**: 1 hour (can be reduced for faster feedback)

### Cost Optimization
- **Min replicas**: 0 (scales to zero when not running)
- **Max replicas**: 1 (single instance sufficient)
- **Execution-based billing**: Only pay when job runs

## 🔄 Maintenance Tasks

### Weekly Tasks
- Review execution logs for failures
- Check performance metrics trends
- Verify screenshot storage usage

### Monthly Tasks
- Review and rotate Key Vault secrets if needed
- Clean up old screenshots (optional)
- Update dependencies in Docker image

### Quarterly Tasks
- Review Azure costs and optimize
- Update Playwright version
- Security review of permissions

## 📞 Support Commands

### Emergency Stop
```bash
# Stop all running executions
az containerapp job stop --name eon-monitoring-job --resource-group RG-EDGMON-RUN
```

### Quick Health Check
```bash
# One-liner to check overall health
az containerapp job execution list --name eon-monitoring-job --resource-group RG-EDGMON-RUN --query "[0].{Status:properties.status,StartTime:properties.startTime}" --output table
```

### Resource Usage
```bash
# Check resource consumption
az containerapp job show --name eon-monitoring-job --resource-group RG-EDGMON-RUN --query "properties.template.containers[0].resources" --output table
```