from azure.storage.fileshare import ShareServiceClient
import uuid
import datetime

def create_empty_file_rest_api(storage_account_name, share_name, sas_token=None, connection_string=None):
    """
    Creates an empty file with a random name in Azure File Share using REST API (port 443).
    This avoids the SMB (port 445) connectivity issues.
    """
    # Generate a random file name
    random_id = str(uuid.uuid4())
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    file_name = f"empty_file_{timestamp}_{random_id}.txt"
    
    # Create the ShareServiceClient
    if connection_string:
        service_client = ShareServiceClient.from_connection_string(connection_string)
    else:
        service_client = ShareServiceClient(
            account_url=f"https://{storage_account_name}.file.core.windows.net", 
            credential=sas_token
        )
    
    # Get the share client
    share_client = service_client.get_share_client(share_name)
    
    # Get the directory client (root in this case)
    directory_client = share_client.get_directory_client("")
    
    # Get the file client
    file_client = directory_client.get_file_client(file_name)
    
    # Create the empty file
    file_client.create_file(size=0)
    
    print(f"Created empty file: {file_name} in share: {share_name}")
    return file_name

if __name__ == "__main__":
    # Replace with your storage account name and share name
    storage_account_name = "rgedgmonrunba36"
    share_name = "mon-script-0000"
    sas_token = "sv=2024-11-04&ss=bfqt&srt=sco&sp=rwdlacupiyx&se=2027-05-09T20:45:22Z&st=2025-05-09T12:45:22Z&spr=https&sig=xLtK52EfgKihWhGHMOZ3jAfVhHDloLdRILUobNE%2F7c8%3D"  # Remplacez par votre jeton SAS
    # OU
    # connection_string = "YOUR_CONNECTION_STRING"  # Remplacez par votre chaîne de connexion
    
    try:
        file_name = create_empty_file_rest_api(
            storage_account_name, 
            share_name, 
            sas_token=sas_token
            # connection_string=connection_string  # Décommentez si vous utilisez une chaîne de connexion
        )
        print(f"Successfully created file: {file_name}")
    except Exception as e:
        print(f"An error occurred: {str(e)}")
