az container create \
  --resource-group RG-EDGMON-RUN \
  --name test-comparative \
  --image scriptmonregistry2025.azurecr.io/web-tester:fixed \
  --cpu 1 \
  --memory 1 \
  --os-type Linux \
  --restart-policy Never \
  --registry-login-server scriptmonregistry2025.azurecr.io \
  --registry-username SCRIPTMONREGISTRY2025 \
  --registry-password "****************************************************" \
  --command-line "/bin/bash -c 'echo === COMPARATIVE TEST === && echo && echo 1. DNS TEST GOOGLE: && nslookup google.com && echo && echo 2. DNS TEST SAP: && nslookup vhinoev1cs.fra3.rise.apps.eon.com && echo && echo 3. PING TEST GOOGLE: && ping -c 2 google.com && echo && echo 4. PING TEST SAP: && ping -c 2 vhinoev1cs.fra3.rise.apps.eon.com && echo && echo 5. CURL TEST GOOGLE: && curl -I --connect-timeout 10 https://google.com && echo && echo 6. CURL TEST SAP: && curl -I --connect-timeout 10 https://vhinoev1cs.fra3.rise.apps.eon.com:44300 && echo && echo === END TESTS ==='"




sleep 20

echo "=== COMPARATIVE TEST LOGS ==="
az container logs --resource-group RG-EDGMON-RUN --name test-comparative


====================================================================================================================

# Google test
az container create \
  --resource-group RG-EDGMON-RUN \
  --name test-python-google \
  --image scriptmonregistry2025.azurecr.io/web-tester:fixed \
  --cpu 1 \
  --memory 1 \
  --os-type Linux \
  --restart-policy Never \
  --registry-login-server scriptmonregistry2025.azurecr.io \
  --registry-username SCRIPTMONREGISTRY2025 \
  --registry-password "****************************************************" \
  --environment-variables \
    TEST_URL="https://google.com" \
    TIMEOUT=30


echo ""
echo "=== PYTHON GOOGLE LOGS ==="
az container logs --resource-group RG-EDGMON-RUN --name test-python-google


echo "=== EXIT STATUS ==="
az container show --resource-group RG-EDGMON-RUN --name test-python-google --query "containers[0].instanceView.currentState"



====================================================================================================================


# SAP test
az container create \
  --resource-group RG-EDGMON-RUN \
  --name test-python-sap \
  --image scriptmonregistry2025.azurecr.io/web-tester:fixed \
  --cpu 1 \
  --memory 1 \
  --os-type Linux \
  --restart-policy Never \
  --registry-login-server scriptmonregistry2025.azurecr.io \
  --registry-username SCRIPTMONREGISTRY2025 \
  --registry-password "****************************************************" \
  --environment-variables \
    TEST_URL="https://vhinoev1cs.fra3.rise.apps.eon.com:44300/sap/bc/gui/sap/its/webgui/" \
    TIMEOUT=60



echo ""
echo "=== PYTHON SAP LOGS ==="
az container logs --resource-group RG-EDGMON-RUN --name test-python-sap


echo "=== EXIT STATUS ==="
az container show --resource-group RG-EDGMON-RUN --name test-python-sap --query "containers[0].instanceView.currentState"