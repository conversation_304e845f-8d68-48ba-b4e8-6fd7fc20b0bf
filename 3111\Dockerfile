# Utiliser une image Python avec support pour Playwright
FROM mcr.microsoft.com/playwright/python:v1.41.1

# Installer les dépendances pour pymssql au lieu de pyodbc
RUN apt-get update && apt-get install -y \
    freetds-bin \
    freetds-dev \
    freetds-common \
    libsybdb5 \
    gnupg \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Installer tzdata et configurer le fuseau horaire de manière non-interactive
ENV DEBIAN_FRONTEND=noninteractive
RUN apt-get update && apt-get install -y tzdata
# Définir le fuseau horaire sur Europe/Berlin (Allemagne)
ENV TZ=Europe/Berlin
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
# Réinitialiser DEBIAN_FRONTEND après l'installation
ENV DEBIAN_FRONTEND=

# Définir le répertoire de travail
WORKDIR /app

# Copier les fichiers de requirements
COPY requirements.txt .

# Installer les dépendances Python
RUN pip install --no-cache-dir -r requirements.txt

# Installer les navigateurs Playwright
RUN playwright install chromium

# Copier le script Python
COPY eonde_test_azure.py .

# Créer les répertoires nécessaires
RUN mkdir -p /mnt/monitoring/results /mnt/monitoring/img

# Définir les variables d'environnement
ENV AZURE_CLIENT_ID=""
ENV PYTHONUNBUFFERED=1
ENV ODBCSYSINI=/etc
ENV ODBCINI=/etc/odbc.ini

# Exposer le port (optionnel pour Container Apps)
EXPOSE 8080

# Commande par défaut
CMD ["python", "eonde_test_azure.py"]




