from playwright.sync_api import Playwright, sync_playwright
import os
import sys
import time
from datetime import date, datetime
import uuid
import json
import logging
import ssl
import urllib3
import requests
# import pyodbc
import pymssql
import datetime as dt

# Disable SSL verification at system level
os.environ['PYTHONHTTPSVERIFY'] = '0'
os.environ['CURL_CA_BUNDLE'] = ''
os.environ['REQUESTS_CA_BUNDLE'] = ''

# Only import Azure libraries, no pyodbc
try:
    from azure.keyvault.secrets import SecretClient
    from azure.identity import DefaultAzureCredential
    from azure.storage.fileshare import ShareFileClient, ShareDirectoryClient
    print("✅ Azure libraries imported successfully")
except ImportError as e:
    print(f"❌ Azure library import error: {e}")
    sys.exit(1)

# Disable SSL warnings and certificate verification
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
ssl._create_default_https_context = ssl._create_unverified_context

# Logging configuration
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Disable logs from Azure SDK libraries
logging.getLogger('azure').setLevel(logging.ERROR)
logging.getLogger('azure.identity').setLevel(logging.ERROR)
logging.getLogger('azure.core').setLevel(logging.ERROR)
logging.getLogger('azure.keyvault').setLevel(logging.ERROR)
logging.getLogger('azure.storage').setLevel(logging.ERROR)
logging.getLogger('azure.storage.fileshare').setLevel(logging.ERROR)  # Specifically for File Share
logging.getLogger('azure.core.pipeline').setLevel(logging.ERROR)      # For HTTP pipelines
logging.getLogger('azure.core.pipeline.policies').setLevel(logging.ERROR)  # For pipeline policies
logging.getLogger('msal').setLevel(logging.ERROR)
logging.getLogger('urllib3').setLevel(logging.ERROR)
logging.getLogger('requests').setLevel(logging.ERROR)

# Create a specific logger for DB with more details
db_logger = logging.getLogger('database')
db_logger.setLevel(logging.DEBUG)
db_handler = logging.StreamHandler()
db_formatter = logging.Formatter('%(asctime)s - DB - %(levelname)s - %(message)s')
db_handler.setFormatter(db_formatter)
db_logger.addHandler(db_handler)
db_logger.propagate = False  # Avoid log duplication

class SimpleEonMonitoring:
    def __init__(self):
        # Azure Configuration
        self.keyvault_name = "scriptsmonitoring2025"
        self.keyvault_url = f"https://{self.keyvault_name}.vault.azure.net/"
        self.storage_account_name = "scriptsmonitoring2025"
        self.file_share_name = "script-monitoring"
        self.container_name = os.environ.get("SCRIPT_ID", "3110")

        # SAS Token with all permissions
        self.sas_token = "sv=2024-11-04&ss=bfqt&srt=sco&sp=rwlacupiytfx&se=2099-05-27T16:12:28Z&st=2025-05-27T08:12:28Z&spr=https&sig=aC8CDIMg0AP4NbymnSwflWBaGKG7sFpswl%2FcslzuqPY%3D"

        # Splunk HEC Configuration
        self.splunk_hec_url = "https://splunk-hec.eon.com:443/services/collector/event"
        self.splunk_token = "bbab61ff-f874-4e71-8403-257370da6485"
        self.splunk_sourcetype = "http_eek_ng_monitoring"
        self.splunk_index = "idx_eek_robots"
        self.splunk_host = f"azure_mon_{os.environ.get('SCRIPT_ID', '3110')}"

        # Initialisation des clients Azure
        try:
            self.credential = DefaultAzureCredential()
            self.secret_client = SecretClient(vault_url=self.keyvault_url, credential=self.credential)
            logger.info("✅ Azure clients initialized successfully")
            
            # Récupérer les credentials depuis Key Vault
            self.email = self.get_secret("eon-email")
            self.password = self.get_secret("eon-password")
            
            if not self.email or not self.password:
                logger.error("❌ Unable to retrieve credentials from Key Vault")
                raise Exception("Missing credentials")
            
            logger.info(f"✅ Credentials retrieved for email: {self.email[:5]}***")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Azure clients: {str(e)}")
            raise

        # Chemins locaux
        self.local_results_path = "/tmp/monitoring/results"
        self.local_img_path = "/tmp/monitoring/img"

        # Créer les répertoires locaux
        os.makedirs(self.local_results_path, exist_ok=True)
        os.makedirs(self.local_img_path, exist_ok=True)

        logger.info("✅ Simple EON Monitoring initialized")

    def get_secret(self, secret_name):
        """Retrieves a secret from Azure Key Vault"""
        try:
            retrieved_secret = self.secret_client.get_secret(secret_name)
            logger.info(f"✅ Secret {secret_name} retrieved successfully")
            return retrieved_secret.value
        except Exception as e:
            logger.error(f"❌ Error retrieving secret {secret_name}: {str(e)}")
            return None

    def create_azure_directory_if_not_exists(self, directory_path):
        """Creates directory structure in Azure File Share if it doesn't exist"""
        try:
            # Split the path into components
            path_parts = directory_path.strip('/').split('/')
            current_path = ""
            
            # Create each directory level if needed
            for part in path_parts:
                if part:
                    if current_path:
                        current_path = f"{current_path}/{part}"
                    else:
                        current_path = part
                        
                    # Create directory client
                    directory_client = ShareDirectoryClient(
                        account_url=f"https://{self.storage_account_name}.file.core.windows.net",
                        share_name=self.file_share_name,
                        directory_path=current_path,
                        credential=self.sas_token,
                        connection_verify=False
                    )
                    
                    # Create directory if it doesn't exist
                    if not directory_client.exists():
                        directory_client.create_directory()
                        logger.info(f"✅ Created directory: {current_path}")
            
            return True
        except Exception as e:
            logger.error(f"❌ Error creating directory {directory_path}: {str(e)}")
            return False

    def upload_to_azure_storage(self, local_file_path, remote_file_path):
        """Uploads a file to Azure File Share using SAS Token"""
        try:
            # Create parent directories if they don't exist
            directory_path = os.path.dirname(remote_file_path)
            if directory_path:
                self.create_azure_directory_if_not_exists(directory_path)
            
            # Use SAS Token instead of DefaultAzureCredential to avoid SSL issues
            file_client = ShareFileClient(
                account_url=f"https://{self.storage_account_name}.file.core.windows.net",
                share_name=self.file_share_name,
                file_path=remote_file_path,
                credential=self.sas_token,
                connection_verify=False  # Disable SSL verification
            )

            with open(local_file_path, "rb") as data:
                file_client.upload_file(data)

            logger.info(f"✅ File uploaded successfully: {remote_file_path}")
            return True
        except Exception as e:
            logger.error(f"❌ Error uploading file {local_file_path}: {str(e)}")
            return False

    def send_to_splunk(self, event_data):
        """Sends event data to Splunk HEC"""
        try:
            # Create epoch timestamp with milliseconds from ISO timestamp
            timestamp_iso = event_data["timestamp"]
            
            # If timestamp is an ISO string
            if isinstance(timestamp_iso, str):
                timestamp_dt = datetime.fromisoformat(timestamp_iso.replace('Z', '+00:00'))
            else:
                # If it's already a datetime object
                timestamp_dt = timestamp_iso
            
            epoch_time = timestamp_dt.timestamp()  # Keep milliseconds (float)

            # Build Splunk event in required format
            splunk_event = {
                "time": epoch_time,
                "sourcetype": self.splunk_sourcetype,
                "event": {
                    "timestamp": event_data["timestamp"],
                    "date": event_data["date"],
                    "event_time": event_data["time"],
                    "script_id": event_data["script_id"],
                    "metrics": event_data["metrics"],
                    "total_time": event_data["total_time"],
                    "success": event_data["success"],
                    "type": "New generation monitoring"
                }
            }

            # Headers for Splunk HEC
            headers = {
                "Authorization": f"Splunk {self.splunk_token}",
                "Content-Type": "application/json"
            }

            logger.info("📦 Payload:")
            payload_str = json.dumps(splunk_event, indent=2)
            logger.info(f"{payload_str}")
            logger.info("=" * 60)

            # Send event to Splunk
            logger.info("🚀 Sending request to Splunk HEC...")
            response = requests.post(
                self.splunk_hec_url,
                headers=headers,
                json=splunk_event,
                verify=False,  # Disable SSL verification
                timeout=30
            )

            # Detailed log of the Splunk HEC response
            logger.info("=" * 60)
            logger.info("📥 SPLUNK HEC RESPONSE DETAILS")
            logger.info("=" * 60)
            logger.info(f"📊 Status Code: {response.status_code}")
            logger.info("📋 Response Headers:")
            for key, value in response.headers.items():
                logger.info(f"    '{key}': '{value}'")

            logger.info(f"📄 Response Body:")
            try:
                if response.text:
                    # Try to parse as JSON for formatted output
                    try:
                        response_json = response.json()
                        logger.info(json.dumps(response_json, indent=2))
                    except:
                        logger.info(f"{response.text}")
                else:
                    logger.info("(Empty response body)")
            except Exception as resp_error:
                logger.warning(f"⚠️ Could not read response body: {str(resp_error)}")

            logger.info("=" * 60)

            if response.status_code == 200:
                logger.info("✅ Event sent to Splunk successfully")
                return True
            else:
                logger.error(f"❌ Splunk HEC error: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"❌ Error sending event to Splunk: {str(e)}")
            return False

    def send_to_new_relic(self, event_data):
        """Sends event data to New Relic Logs API"""
        try:
            # New Relic Log API settings
            new_relic_log_api_url = 'https://log-api.eu.newrelic.com/log/v1'
            new_relic_api_key = 'eu01xx0a982ff080c0b184b6137750a8FFFFNRAL'

            # Convert ISO timestamp to epoch
            from datetime import datetime
            import time
            
            # If timestamp is already in ISO format
            if isinstance(event_data["timestamp"], str):
                dt_obj = datetime.fromisoformat(event_data["timestamp"].replace('Z', '+00:00'))
                epoch_time = str(int(dt_obj.timestamp()))
            else:
                # If it's already a datetime object
                epoch_time = str(int(time.time()))

            # Prepare data for New Relic with specified format
            new_relic_payload = {
                "timestamp": epoch_time,
                "message": f"EON Monitoring Event - script {event_data['script_id']}",
                "attributes": {
                    "date": event_data["date"],
                    "event_time": event_data["time"],
                    "script_id": event_data["script_id"],
                    "metrics": event_data["metrics"],
                    "total_time": event_data["total_time"],
                    "success": event_data["success"],
                    "type": "New generation monitoring",
                    "source": f"azure_mon_{os.environ.get('SCRIPT_ID', '3110')}"
                }
            }

            # Headers pour New Relic
            headers = {
                "Api-Key": new_relic_api_key,
                "Content-Type": "application/json"
            }

            logger.info("📦 Payload:")
            payload_str = json.dumps(new_relic_payload, indent=2)
            logger.info(f"{payload_str}")
            logger.info("=" * 60)

            # Envoyer les données à New Relic
            logger.info("🚀 Sending request to New Relic Logs API...")
            response = requests.post(
                new_relic_log_api_url,
                headers=headers,
                json=new_relic_payload,
                verify=False,  # Désactiver la vérification SSL
                timeout=30
            )

            # Log détaillé de la réponse New Relic
            logger.info("=" * 60)
            logger.info("📥 NEW RELIC API RESPONSE DETAILS")
            logger.info("=" * 60)
            logger.info(f"📊 Status Code: {response.status_code}")
            logger.info("📋 Response Headers:")
            for key, value in response.headers.items():
                logger.info(f"    '{key}': '{value}'")

            logger.info(f"📄 Response Body:")
            try:
                if response.text:
                    try:
                        response_json = response.json()
                        logger.info(json.dumps(response_json, indent=2))
                    except:
                        logger.info(f"{response.text}")
                else:
                    logger.info("(Empty response body)")
            except Exception as resp_error:
                logger.warning(f"⚠️ Could not read response body: {str(resp_error)}")

            logger.info("=" * 60)

            if response.status_code in [200, 202]:  # New Relic renvoie généralement 202 Accepted
                logger.info("✅ Event sent to New Relic successfully")
                return True
            else:
                logger.error(f"❌ New Relic API error: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"❌ Error sending event to New Relic: {str(e)}")
            return False

    def log_results(self, metrics_data):
        """Logs results locally and to Azure (CSV + JSON) and sends to external services"""
        try:
            # Configure timezone explicitly
            import pytz
            local_tz = pytz.timezone('Europe/Berlin')  # German timezone
            
            # Get current local time
            now_utc = datetime.now(pytz.UTC)
            now_local = now_utc.astimezone(local_tz)
            
            # 1. CSV Log (original format)
            script_id = os.environ.get("SCRIPT_ID", "3110")
            csv_file_path = os.path.join(self.local_results_path, f"{script_id}_RTR.csv")

            # Create CSV header if needed
            if not os.path.exists(csv_file_path):
                with open(csv_file_path, 'w') as f:
                    header = "SCRIPT_ID;SCRIPT_DATE;SCRIPT_START_TIME;LOAD_LOGIN_PAGE;LOGIN;METER_READING;ABSCHLAG;DOWNLOAD_BILLS;LOGOUT;\n"
                    f.write(header)
                logger.info("✅ CSV header created")

            # IMPORTANT: metrics_data ALREADY contains milliseconds, NO conversion needed
            # Log to debug values
            logger.info("🔍 DEBUG - Metrics values (already in milliseconds):")
            logger.info(f"  load_login_page: {metrics_data['load_login_page']} ms")
            logger.info(f"  login: {metrics_data['login']} ms")
            logger.info(f"  meter_reading: {metrics_data['meter_reading']} ms")
            logger.info(f"  abschlag: {metrics_data['abschlag']} ms")
            logger.info(f"  logout: {metrics_data['logout']} ms")

            # Add CSV data
            with open(csv_file_path, 'a') as f:
                csv_line = f"{os.environ.get('SCRIPT_ID', '3110')};{now_local.strftime('%d.%m.%Y')};{now_local.strftime('%H:%M:%S')};{metrics_data['load_login_page']};{metrics_data['login']};{metrics_data['meter_reading']};{metrics_data['abschlag']};{metrics_data['download_bills']};{metrics_data['logout']};\n"
                f.write(csv_line)

            logger.info(f"✅ Data written to local CSV: {csv_line.strip()}")

            # 2. JSON Log (more detailed)
            json_file_path = os.path.join(self.local_results_path, f"{os.environ.get('SCRIPT_ID', '3110')}_detailed.json")

            detailed_data = {
                "timestamp": now_local.isoformat(),
                "date": now_local.strftime('%d.%m.%Y'),
                "time": now_local.strftime('%H:%M:%S'),
                "script_id": os.environ.get("SCRIPT_ID", "3110"),
                "metrics": metrics_data,  # Use values in ms directly
                "total_time": sum([v for v in metrics_data.values() if v > 0]),
                "success": metrics_data['login'] > 0,
                "type": "New generation monitoring"
            }

            # Lire les données existantes ou créer un nouveau fichier
            json_data = []
            if os.path.exists(json_file_path):
                try:
                    with open(json_file_path, 'r') as f:
                        json_data = json.load(f)
                except:
                    json_data = []

            json_data.append(detailed_data)

            # Garder seulement les 100 dernières entrées
            json_data = json_data[-100:]

            with open(json_file_path, 'w') as f:
                json.dump(json_data, f, indent=2)

            logger.info("✅ Data written to JSON log")

            # 3. Upload vers Azure File Share
            csv_remote_path = f"{self.container_name}/{os.environ.get('SCRIPT_ID', '3110')}_RTR.csv"
            json_remote_path = f"{self.container_name}/{os.environ.get('SCRIPT_ID', '3110')}_detailed.json"

            csv_uploaded = self.upload_to_azure_storage(csv_file_path, csv_remote_path)
            json_uploaded = self.upload_to_azure_storage(json_file_path, json_remote_path)

            if csv_uploaded and json_uploaded:
                logger.info("✅ All results successfully uploaded to Azure Storage")
            else:
                logger.warning("⚠️ Some files failed to upload to Azure Storage")

            # 4. Send event to Splunk HEC
            logger.info("📡 Sending event to Splunk HEC...")
            splunk_sent = self.send_to_splunk(detailed_data)

            if splunk_sent:
                logger.info("✅ Event successfully sent to Splunk")
            else:
                logger.warning("⚠️ Failed to send event to Splunk")
            
            # 5. Send event to New Relic
            logger.info("📡 Sending event to New Relic...")
            new_relic_sent = self.send_to_new_relic(detailed_data)
            
            if new_relic_sent:
                logger.info("✅ Event successfully sent to New Relic")
            else:
                logger.warning("⚠️ Failed to send event to New Relic")

            # 6. Log en DB uniquement si le login a réussi
            if metrics_data['login'] > 0:
                logger.info("📊 Logging to database...")
                try:
                    # Utiliser directement la valeur en ms
                    self.logdb(str(metrics_data['login']))
                    logger.info("✅ Database logging completed")
                except Exception as db_error:
                    logger.error(f"❌ Database logging failed: {str(db_error)}")
            else:
                logger.warning("⚠️ Login failed, skipping database logging")

            return True

        except Exception as e:
            logger.error(f"❌ Error in log_results function: {str(e)}")
            return False

    def format_ms_for_display(self, ms_value):
        """Formate une valeur en millisecondes pour l'affichage"""
        if ms_value <= 0:
            return "0"
        return str(ms_value)

    def take_screenshot(self, page, step_name):
        """Prend un screenshot et l'upload vers Azure"""
        try:
            # Debug: Log current URL and page title
            current_url = page.url
            try:
                page_title = page.title()
            except:
                page_title = "Unknown"

            logger.info(f"📸 Taking screenshot for {step_name}")
            logger.info(f"📍 URL: {current_url}")
            logger.info(f"📄 Title: {page_title}")

            # Wait for page to be stable before screenshot
            try:
                logger.info("⏳ Waiting for page to be stable...")
                page.wait_for_load_state("networkidle", timeout=10000)
                logger.info("✅ Page networkidle state reached")

                # Wait even longer for dynamic content
                logger.info("⏳ Waiting additional time for dynamic content...")
                page.wait_for_timeout(5000)
                logger.info("✅ Additional wait completed")

                # Check if there is visible content
                try:
                    body_content = page.locator("body").inner_text()
                    if len(body_content.strip()) > 0:
                        logger.info(f"✅ Page has content ({len(body_content)} characters)")
                    else:
                        logger.warning("⚠️ Page body appears to be empty")
                except:
                    logger.warning("⚠️ Could not read page content")

            except Exception as wait_error:
                logger.warning(f"⚠️ Page load timeout: {str(wait_error)}, taking screenshot anyway")

            timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
            filename = f"{os.environ.get('SCRIPT_ID', '3110')}_{timestamp}_{step_name}.png"
            local_path = os.path.join(self.local_img_path, filename)

            # Prendre un screenshot en pleine page
            logger.info("📸 Capturing screenshot...")
            page.screenshot(path=local_path, full_page=True)
            logger.info(f"✅ Screenshot saved locally: {filename}")

            # Vérifier la taille du fichier
            try:
                file_size = os.path.getsize(local_path)
                logger.info(f"📊 Screenshot file size: {file_size} bytes")
            except:
                logger.warning("⚠️ Could not check screenshot file size")

            # Upload vers Azure
            remote_path = f"{self.container_name}/screenshots/{filename}"
            upload_success = self.upload_to_azure_storage(local_path, remote_path)

            if upload_success:
                logger.info(f"✅ Screenshot uploaded: {filename}")

            return local_path

        except Exception as e:
            logger.error(f"❌ Screenshot error: {str(e)}")
            return None

    def test_db_connection(self):
        """Teste la connexion à la base de données Azure SQL"""
        db_logger = logging.getLogger('db_logger')
        db_logger.setLevel(logging.INFO)
        
        try:
            db_logger.info("========== DATABASE CONNECTION TEST ==========")
            
            # Récupérer les credentials de la DB depuis Key Vault avec le préfixe correct
            db_logger.info("Retrieving database credentials from Key Vault")
            db_server = self.get_secret(f"{os.environ.get('SCRIPT_ID', '3110')}-db-server")
            db_logger.info(f"DB Server retrieved: {db_server}")
            
            db_name = self.get_secret(f"{os.environ.get('SCRIPT_ID', '3110')}-db-name")
            db_logger.info(f"DB Name retrieved: {db_name}")
            
            db_username = self.get_secret(f"{os.environ.get('SCRIPT_ID', '3110')}-db-username")
            db_logger.info(f"DB Username retrieved: {db_username}")
            
            db_password = self.get_secret(f"{os.environ.get('SCRIPT_ID', '3110')}-db-password")
            db_logger.info("DB Password retrieved: [MASKED]")
            
            # Valeurs par défaut si les secrets ne sont pas trouvés
            if not db_server:
                db_server = "b2smepcw-sql-12345.database.windows.net"
                db_logger.info(f"Using default DB Server: {db_server}")
            
            if not db_name:
                db_name = "EON_MON_DB"
                db_logger.info(f"Using default DB Name: {db_name}")
            
            if not db_username:
                db_username = "sa1bmon"
                db_logger.info(f"Using default DB Username: {db_username}")
            
            if not db_password:
                db_password = "Affe1212"
                db_logger.info("Using default DB Password: [MASKED]")
            
            # Tester la connexion avec pymssql
            db_logger.info("Attempting to connect with pymssql...")
            conn = pymssql.connect(
                server=db_server,
                user=db_username,
                password=db_password,
                database=db_name
            )
            db_logger.info("Connection successful!")
            
            # Tester l'accès aux tables
            cursor = conn.cursor()
            db_logger.info("Testing access to tables...")
            
            # Vérifier les tables
            cursor.execute("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME IN ('Measurement', 'RESULT')")
            tables = [row[0] for row in cursor.fetchall()]
            db_logger.info(f"Found tables: {tables}")
            
            # Vérifier les colonnes
            for table in tables:
                cursor.execute(f"SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '{table}'")
                columns = cursor.fetchall()
                db_logger.info(f"Table {table} structure:")
                for col in columns:
                    db_logger.info(f"  - {col[0]}: {col[1]}")
            
            # Tester une requête simple
            db_logger.info("Testing a simple query...")
            cursor.execute("SELECT TOP 1 * FROM Measurement")
            row = cursor.fetchone()
            if row:
                db_logger.info("Query successful, sample data retrieved")
            else:
                db_logger.info("Query successful, but no data found in Measurement table")
            
            cursor.close()
            conn.close()
            db_logger.info("Database connection test completed successfully")
            return True
        
        except Exception as e:
            db_logger.error(f"Database connection test failed: {str(e)}")
            import traceback
            db_logger.error(traceback.format_exc())
            return False

    def logdb(self, login_ms_value):
        """Log les résultats dans la base de données"""
        db_logger.info("========== DATABASE OPERATION STARTED ==========")
        db_logger.info(f"Logging login time: {login_ms_value}ms")
        
        try:
            # Récupérer les informations de connexion depuis Key Vault
            db_server = self.get_secret("db-server")
            db_name = self.get_secret("db-name")
            db_username = self.get_secret("db-username")
            db_password = self.get_secret("db-password")
            
            if not all([db_server, db_name, db_username, db_password]):
                db_logger.error("❌ Failed to retrieve database credentials from Key Vault")
                return False
            
            db_logger.info("✅ Database credentials retrieved from Key Vault")
            
            # Connexion à la base de données avec pymssql
            import pymssql
            
            db_logger.info(f"Connecting to database server: {db_server}")
            conn = pymssql.connect(
                server=db_server,
                database=db_name,
                user=db_username,
                password=db_password
            )
            db_logger.info("✅ Database connection successful with pymssql")
            
            cursor = conn.cursor()
            db_logger.info("✅ Cursor created")

            MID = str(uuid.uuid4())
            TIMESTAMP = dt.datetime.now().strftime("%Y-%m-%d %H:%M:%S") + ".000"
            db_logger.info(f"Generated MID: {MID}")
            db_logger.info(f"Generated TIMESTAMP: {TIMESTAMP}")

            # Insérer dans la table Measurement
            sql_measurement = "INSERT INTO Measurement (MID,SCRIPTID,TIMESTAMP,STATIONID) VALUES (%s,%s,%s,%s)"
            db_logger.info(f"Executing SQL: {sql_measurement}")
            db_logger.info(f"With parameters: MID='{MID}', SCRIPTID='{os.environ.get('SCRIPT_ID', '3110')}', TIMESTAMP='{TIMESTAMP}', STATIONID=200")
            
            cursor.execute(sql_measurement, (MID, os.environ.get('SCRIPT_ID', '3110'), TIMESTAMP, 200))
            db_logger.info("✅ Measurement record inserted")
            
            # Insérer dans la table RESULT
            sql_result = "INSERT INTO RESULT (MID,NAME,VALUE) VALUES (%s,%s,%s)"
            db_logger.info(f"Executing SQL: {sql_result}")
            db_logger.info(f"With parameters: MID='{MID}', NAME='LOGIN', VALUE='{login_ms_value}'")
            
            cursor.execute(sql_result, (MID, 'LOGIN', login_ms_value))
            db_logger.info("✅ Result record inserted")
            
            db_logger.info("Committing transaction...")
            conn.commit()
            db_logger.info("✅ Transaction committed")
            
            cursor.close()
            conn.close()
            db_logger.info("✅ Database connection closed")
            
            db_logger.info(f"✅ DB LOGGING COMPLETE - Login time {login_ms_value}ms recorded")
            db_logger.info("========== DATABASE OPERATION ENDED ==========")
            
            return True
        
        except Exception as e:
            db_logger.error(f"❌ Database error: {str(e)}")
            import traceback
            db_logger.error(traceback.format_exc())
            db_logger.info("========== DATABASE OPERATION FAILED ==========")
            return False

    def handle_feedback_popup(self, page):
        """Handle the feedback popup that may appear after certain actions"""
        try:
            # Check if the popup is visible
            feedback_popup = page.locator("text=Ihre Meinung zählt!")
            
            if feedback_popup.is_visible(timeout=3000):
                logger.info("📊 Feedback popup detected")
                
                # Try to close it using the "Schließen" button
                close_button = page.get_by_text("Schließen")
                if close_button.is_visible():
                    close_button.click()
                    logger.info("✅ Closed feedback popup using 'Schließen' button")
                    return True
                
                # Alternative: try to click "Teilnehmen" button
                participate_button = page.get_by_text("Teilnehmen")
                if participate_button.is_visible():
                    # We don't want to participate, so we'll look for a way to close
                    # the survey that might appear after clicking this
                    participate_button.click()
                    logger.info("ℹ️ Clicked 'Teilnehmen' button")
                    
                    # Wait a bit for any survey to load
                    page.wait_for_timeout(2000)
                    
                    # Try to find and click a close button on the survey
                    survey_close = page.locator("button").filter(has_text="Schließen").first
                    if survey_close.is_visible(timeout=2000):
                        survey_close.click()
                        logger.info("✅ Closed survey after feedback popup")
                        return True
            
                logger.warning("⚠️ Could not close feedback popup")
                return False
        except Exception as e:
            logger.info(f"ℹ️ No feedback popup or error: {str(e)}")
            return False

    def run(self, playwright: Playwright) -> None:
        """Main monitoring execution function"""
        logger.info("=" * 50)
        logger.info("🚀 STARTING EON MONITORING (SIMPLE VERSION)")
        logger.info("=" * 50)

        try:
            # Test de connectivité Azure
            logger.info("🔐 Testing Azure connectivity...")
            test_secret = self.get_secret("eon-email")
            if not test_secret:
                logger.error("❌ Cannot retrieve test secret from Key Vault")
                sys.exit(1)

            # Récupérer les credentials depuis Key Vault
            email = self.get_secret("eon-email")
            password = self.get_secret("eon-password")

            if not email or not password:
                logger.error("❌ Unable to retrieve credentials from Key Vault")
                sys.exit(1)

            logger.info(f"✅ Credentials retrieved for email: {email[:5]}***")

            # Configuration du navigateur
            logger.info("🌐 Launching browser...")
            browser = playwright.chromium.launch(
                headless=True,  # Retour en mode headless maintenant qu'on connaît le problème
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-extensions',
                    '--disable-background-timer-throttling',
                    '--disable-renderer-backgrounding',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-blink-features=AutomationControlled',  # Éviter la détection d'automatisation
                    '--ignore-ssl-errors',  # Ignorer les erreurs SSL
                    '--ignore-certificate-errors',  # Ignorer les erreurs de certificat
                    '--ignore-ssl-errors-list',  # Ignorer la liste des erreurs SSL
                    '--ignore-certificate-errors-spki-list',  # Ignorer les erreurs SPKI
                    '--disable-web-security',  # Désactiver la sécurité web
                    '--allow-running-insecure-content',  # Permettre le contenu non sécurisé
                    '--ignore-urlfetcher-cert-requests',  # Ignorer les requêtes de certificat
                    '--disable-features=VizDisplayCompositor',  # Désactiver le compositeur d'affichage
                    '--ignore-certificate-errors-spki-list',  # Ignorer les erreurs SPKI
                    '--ignore-ssl-errors-list',  # Ignorer la liste des erreurs SSL
                    '--reduce-security-for-testing',  # Réduire la sécurité pour les tests
                    '--allow-running-insecure-content',  # Permettre le contenu non sécurisé
                    '--disable-features=TranslateUI',  # Désactiver l'interface de traduction
                    '--disable-ipc-flooding-protection',  # Désactiver la protection contre le flooding IPC
                    '--test-type',  # Mode test
                    '--disable-default-apps'  # Désactiver les applications par défaut
                ]
            )

            context = browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",  # User-Agent normal
                viewport={'width': 1920, 'height': 1080},  # Résolution plus commune
                ignore_https_errors=True,  # Ignorer les erreurs HTTPS au niveau du contexte
                extra_http_headers={
                    'Accept-Language': 'de-DE,de;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Sec-Fetch-User': '?1',
                    'Upgrade-Insecure-Requests': '1'
                }
            )
            page = context.new_page()
            page.set_default_timeout(60000)  # 60 seconds

            logger.info("✅ Browser launched successfully")

            # Test screenshot to verify browser works
            try:
                page.goto("about:blank")
                self.take_screenshot(page, "BROWSER_TEST")
            except Exception as e:
                logger.warning(f"⚠️ Browser test screenshot failed: {str(e)}")

            # Control variables
            login_page_error = False
            continue_execution = True

            # Performance metrics (initialize with -2000 to indicate "not tested")
            metrics = {
                'load_login_page': -2000,
                'login': -2000,
                'meter_reading': -2000,
                'abschlag': -2000,
                'download_bills': -2000,
                'logout': -2000
            }

            # ========== STEP 1: LOADING LOGIN PAGE ==========
            logger.info("📄 Step 1: Loading login page")
            start_time = time.time()

            try:
                page.goto("https://da.eon.de/s/login/", wait_until='networkidle')

                # Handle consent popups
                try:
                    page.get_by_test_id("uc-accept-all-button").click(timeout=3000)
                    logger.info("✅ Cookie banner accepted")
                except:
                    logger.info("ℹ️ No cookie banner found")

                # Handle data processing consent popup
                try:
                    # Wait a bit for the popup to appear if it should
                    page.wait_for_timeout(2000)

                    # Search for the "Accept" button in the consent popup
                    accept_button = page.get_by_text("Akzeptieren")
                    if accept_button.is_visible(timeout=3000):
                        accept_button.click()
                        logger.info("✅ Data processing consent accepted (Akzeptieren clicked)")
                        page.wait_for_timeout(1000)  # Wait for the popup to close
                    else:
                        logger.info("ℹ️ No data processing consent popup found")
                except Exception as consent_error:
                    logger.info(f"ℹ️ No data processing consent popup or error: {str(consent_error)}")

                # Fill fields with human-like delays to avoid detection
                logger.info("📝 Filling email field...")
                email_field = page.get_by_role("textbox", name="E-Mail-Adresse")
                email_field.click()  # Click first to focus
                page.wait_for_timeout(800)  # Human delay
                email_field.fill(email)
                page.wait_for_timeout(1200)  # Delay between fields

                logger.info("📝 Filling password field...")
                password_field = page.get_by_role("textbox", name="Passwort")
                password_field.click()  # Click first to focus
                page.wait_for_timeout(600)  # Human delay
                password_field.fill(password)
                page.wait_for_timeout(1500)  # Delay before submission

                end_time = time.time()
                # Store directly in milliseconds
                metrics['load_login_page'] = int((end_time - start_time) * 1000)
                logger.info(f"✅ Login page loaded in {metrics['load_login_page']}ms")

            except Exception as e:
                logger.error(f"❌ Login page loading error: {str(e)}")
                metrics['load_login_page'] = -2000  # Timeout/page loading failure
                continue_execution = False
                login_page_error = True
                self.take_screenshot(page, "LOAD_LOGIN_PAGE_ERROR")

            # ========== STEP 2: LOGIN ==========
            if continue_execution:
                logger.info("🔑 Step 2: Performing login")
                start_time = time.time()

                try:
                    page.get_by_role("button", name="Anmelden").click()

                    # Wait for navigation to complete
                    page.wait_for_timeout(10000)

                    # Check the URL after login attempt
                    current_url = page.url
                    logger.info(f"📍 URL after login click: {current_url}")

                    # CHECK IF LOGIN ACTUALLY SUCCEEDED
                    if "/s/login/" in current_url:
                        logger.error("❌ LOGIN FAILED - Still on login page!")
                        logger.error("❌ Possible causes:")
                        logger.error("   - Incorrect credentials")
                        logger.error("   - CAPTCHA required")
                        logger.error("   - Account locked")
                        logger.error("   - Login form changed")

                        # Take screenshot to see what's on the login page
                        self.take_screenshot(page, "LOGIN_FAILED")

                        # Check for error messages on the page
                        try:
                            page_content = page.content()
                            if "error" in page_content.lower() or "fehler" in page_content.lower():
                                logger.error("❌ Error message detected on login page")
                            if "captcha" in page_content.lower():
                                logger.error("❌ CAPTCHA detected - manual intervention required")
                        except:
                            pass

                        # STOP EXECUTION - no point continuing
                        metrics['login'] = -2000  # Timeout/error in login
                        continue_execution = False
                        end_time = time.time()
                        logger.error(f"❌ Login failed after {int((end_time - start_time) * 1000)}ms")

                    else:
                        logger.info("✅ Login appears successful - navigated away from login page")

                        # Handle different post-login scenarios
                        if "chrome-error://" in current_url or "eon.my.site.com" in current_url:
                            logger.info("🔄 Detected problematic redirect, navigating to correct URL...")
                            try:
                                page.goto("https://www.eon.de/de/mein-eon.html", wait_until='networkidle', timeout=30000)
                                logger.info("✅ Direct navigation to mein-eon.html successful")
                                current_url = page.url
                                logger.info(f"📍 New URL after direct navigation: {current_url}")
                            except Exception as direct_nav_error:
                                logger.error(f"❌ Direct navigation failed: {str(direct_nav_error)}")
                                continue_execution = False

                        elif "mein-eon.html" not in current_url and "meineonda" not in current_url:
                            logger.info("🔄 Not on expected dashboard, navigating to mein-eon.html...")
                            try:
                                page.goto("https://www.eon.de/de/mein-eon.html", wait_until='networkidle', timeout=30000)
                                logger.info("✅ Navigation to mein-eon.html successful")
                                current_url = page.url
                                logger.info(f"📍 Final URL: {current_url}")
                            except Exception as nav_error:
                                logger.error(f"❌ Navigation to dashboard failed: {str(nav_error)}")
                                continue_execution = False

                    # Only continue with post-login actions if login was successful
                    if continue_execution and "/s/login/" not in current_url:
                        try:
                            page.get_by_text("Ablehnen").click(timeout=5000)
                            logger.info("✅ Additional consent declined")
                        except:
                            logger.info("ℹ️ No additional consent dialog")

                        # Verify we're on the correct dashboard
                        final_url = page.url
                        logger.info(f"📍 Final URL after login: {final_url}")

                        if "mein-eon.html" in final_url or "meineonda" in final_url:
                            end_time = time.time()
                            # Store directly in milliseconds
                            metrics['login'] = int((end_time - start_time) * 1000)
                            logger.info(f"✅ Login successful in {metrics['login']}ms")

                            # Take screenshot of successful login
                            self.take_screenshot(page, "AFTER_LOGIN_SUCCESS")
                        else:
                            logger.error(f"❌ Login failed - unexpected final URL: {final_url}")
                            metrics['login'] = -2000  # Timeout/error in navigation
                            continue_execution = False
                            self.take_screenshot(page, "LOGIN_UNEXPECTED_URL")

                except Exception as e:
                    logger.error(f"❌ Login error: {str(e)}")
                    metrics['login'] = -2000  # Timeout/error in login
                    continue_execution = False
                    self.take_screenshot(page, "LOGIN_ERROR")

            # ========== STEP 3: METER READING TEST ==========
            if continue_execution:
                logger.info("⚡ Step 3: Testing meter reading")
                start_time = time.time()

                try:
                    page.goto("https://www.eon.de/de/meineonda/mein-zaehlerstand.html", wait_until='networkidle')
                    page.locator("login-meter-read-submission-app").get_by_text("Zählernummer: 9701696").click(timeout=30000)
                    self.handle_feedback_popup(page)

                    end_time = time.time()
                    # Store directly in milliseconds
                    metrics['meter_reading'] = int((end_time - start_time) * 1000)
                    logger.info(f"✅ Meter reading test successful in {metrics['meter_reading']}ms")

                except Exception as e:
                    logger.error(f"❌ Meter reading test error: {str(e)}")
                    metrics['meter_reading'] = -2000  # Timeout/error in meter reading test

            # ========== STEP 4: TESTING INSTALLMENTS ==========
            if continue_execution:
                logger.info("💰 Step 4: Testing installments")
                start_time = time.time()

                try:
                    page.goto("https://www.eon.de/de/meineonda/mein-abschlag.html", wait_until='networkidle')
                    page.get_by_text("Abschlagseingabe").click(timeout=30000)
                    self.handle_feedback_popup(page)

                    end_time = time.time()
                    # Store directly in milliseconds
                    metrics['abschlag'] = int((end_time - start_time) * 1000)
                    logger.info(f"✅ Installment test successful in {metrics['abschlag']}ms")

                except Exception as e:
                    logger.error(f"❌ Installment test error: {str(e)}")
                    metrics['abschlag'] = -2000  # Timeout/error in installment test

            # ========== STEP 5: TESTING BILL DOWNLOAD ==========
            if continue_execution:
                logger.info("📄 Step 5: Testing bill download")
                start_time = time.time()
                
                try:
                    page.goto("https://www.eon.de/de/meineonda/meine-rechnungen.html", wait_until='networkidle')
                    
                    # Take screenshot before clicking download
                    self.take_screenshot(page, "BEFORE_BILL_DOWNLOAD")
                    
                    # Utiliser le bon sélecteur pour le lien de téléchargement
                    download_link = page.locator("a.eonui-link").filter(has_text="Rechnung herunterladen").first
                    
                    # Vérifier si le lien existe
                    if download_link.count() > 0:
                        logger.info("✅ Download link found")
                        
                        # Configurer l'attente du téléchargement
                        with page.expect_download(timeout=60000) as download_info:
                            download_link.click()
                            logger.info("✅ Download link clicked")
                        
                        # Attendre que le téléchargement commence
                        download = download_info.value
                        logger.info(f"✅ Download started: {download.suggested_filename}")
                        
                        # Gérer la popup de feedback qui pourrait apparaître
                        self.handle_feedback_popup(page)

                        # Attendre que le téléchargement se termine
                        download_path = download.path()
                        logger.info(f"✅ Download completed: {download_path}")
                        
                        end_time = time.time()
                        # Store directly in milliseconds
                        metrics['download_bills'] = int((end_time - start_time) * 1000)
                        logger.info(f"✅ Bill download test successful in {metrics['download_bills']}ms")
                    else:
                        logger.error("❌ Download link not found")
                        metrics['download_bills'] = -2000
                    
                    # Take screenshot after download attempt
                    self.take_screenshot(page, "AFTER_BILL_DOWNLOAD")
                    
                except Exception as e:
                    logger.error(f"❌ Bill download test error: {str(e)}")
                    metrics['download_bills'] = -2000  # Timeout/error in bill download test
                    self.take_screenshot(page, "BILL_DOWNLOAD_ERROR")

            # ========== STEP 6: LOGGING OUT ==========
            logger.info("🚪 Step 6: Logging out")
            start_time = time.time()

            try:
                # Plusieurs méthodes pour trouver le bouton de déconnexion
                logout_found = False
                
                # Méthode 1: Utiliser le sélecteur CSS spécifique
                try:
                    logger.info("Trying logout method 1: CSS selector")
                    page.locator("eon-ui-website-navigation-main-link[internal-title='Logout']").click(timeout=5000)
                    logout_found = True
                    logger.info("✅ Logout button found with CSS selector")
                except Exception as e:
                    logger.info(f"Method 1 failed: {str(e)}")
                
                # Méthode 2: Utiliser l'icône
                if not logout_found:
                    try:
                        logger.info("Trying logout method 2: Icon name")
                        page.locator("eon-ui-icon[name='logout']").click(timeout=5000)
                        logout_found = True
                        logger.info("✅ Logout button found with icon name")
                    except Exception as e:
                        logger.info(f"Method 2 failed: {str(e)}")
                
                # Méthode 3: Utiliser le titre
                if not logout_found:
                    try:
                        logger.info("Trying logout method 3: Title attribute")
                        page.locator("a[title='Logout']").click(timeout=5000)
                        logout_found = True
                        logger.info("✅ Logout button found with title attribute")
                    except Exception as e:
                        logger.info(f"Method 3 failed: {str(e)}")
                
                # Méthode 4: Ancienne méthode (fallback)
                if not logout_found:
                    try:
                        logger.info("Trying logout method 4: Original method")
                        page.get_by_role("menuitem", name="Navigiere zu https://www.eon.de/de/pk.html").locator("svg").click(timeout=5000)
                        logout_found = True
                        logger.info("✅ Logout button found with original method")
                    except Exception as e:
                        logger.info(f"Method 4 failed: {str(e)}")
                
                # Vérifier si on a réussi à trouver et cliquer sur le bouton de déconnexion
                if not logout_found:
                    raise Exception("Could not find logout button with any method")
                
                # Attendre la page de confirmation de déconnexion
                logger.info("Waiting for logout confirmation page...")
                
                # Plusieurs méthodes pour vérifier la déconnexion
                logout_confirmed = False
                
                # Méthode 1: Attendre l'URL de déconnexion
                try:
                    page.wait_for_url("https://www.eon.de/de/meineon/logout.html", timeout=10000)
                    logout_confirmed = True
                    logger.info("✅ Logout confirmed by URL")
                except Exception as e:
                    logger.info(f"Logout URL wait failed: {str(e)}")
                
                # Méthode 2: Chercher le message de confirmation
                if not logout_confirmed:
                    try:
                        page.wait_for_selector("text=Sie haben sich erfolgreich abgemeldet", timeout=10000)
                        logout_confirmed = True
                        logger.info("✅ Logout confirmed by success message")
                    except Exception as e:
                        logger.info(f"Logout message wait failed: {str(e)}")
                
                # Si la déconnexion est confirmée, calculer le temps
                if logout_confirmed:
                    end_time = time.time()
                    # Store directly in milliseconds
                    metrics['logout'] = int((end_time - start_time) * 1000)
                    logger.info(f"✅ Logout successful in {metrics['logout']}ms")
                    
                    # Prendre une capture d'écran de la page de déconnexion
                    self.take_screenshot(page, "LOGOUT_SUCCESS")
                else:
                    logger.error("❌ Could not confirm successful logout")
                    metrics['logout'] = -2000  # Échec de déconnexion
                    self.take_screenshot(page, "LOGOUT_FAILURE")

            except Exception as e:
                logger.error(f"❌ Logout error: {str(e)}")
                metrics['logout'] = -2000  # Timeout/error in logout
                self.take_screenshot(page, "LOGOUT_ERROR")

            # Fermeture du navigateur
            context.close()
            browser.close()
            logger.info("✅ Browser closed")

            # ========== ENREGISTREMENT DES RÉSULTATS ==========
            if not login_page_error:
                logger.info("💾 Saving results...")
                # Passer directement les métriques en secondes à log_results
                # La conversion en ms sera faite UNE SEULE FOIS dans log_results
                self.log_results(metrics)
                logger.info("✅ Results saved successfully")
            else:
                logger.error("❌ Execution failed - no results recorded")

        except Exception as e:
            logger.error(f"💥 Critical error during monitoring: {str(e)}")
            sys.exit(1)

        finally:
            logger.info("🏁 EON MONITORING COMPLETED")
            logger.info("=" * 50)

def main():
    """Point d'entrée principal"""
    try:
        logger.info("🎬 Starting EON Monitoring Application (Simple Version)")
        monitoring = SimpleEonMonitoring()

        with sync_playwright() as playwright:
            monitoring.run(playwright)

        logger.info("🎉 EON Monitoring Application completed successfully")

    except Exception as e:
        logger.error(f"💥 Application failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
