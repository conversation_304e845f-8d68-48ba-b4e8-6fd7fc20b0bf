#!/usr/bin/env python3

import os
import pytz
from datetime import datetime

def test_timezone_conversions():
    """Test timezone conversions for different scenarios"""
    print("=" * 60)
    print("🕐 TIMEZONE CONVERSION TEST")
    print("=" * 60)
    
    # Simulate local environment
    print("\n📍 LOCAL ENVIRONMENT SIMULATION")
    print("-" * 40)
    is_container = False
    timezone = None
    
    # Get current local time
    local_time = datetime.now()
    print(f"Local time (naive): {local_time}")
    print(f"Local time ISO: {local_time.isoformat()}")
    
    # For CSV: use local time as-is
    csv_date = local_time.strftime("%d.%m.%Y")
    csv_time = local_time.strftime("%H:%M:%S")
    print(f"CSV format: {csv_date} {csv_time}")
    
    # For Splunk: use local time as-is
    splunk_timestamp = local_time.isoformat()
    print(f"Splunk timestamp: {splunk_timestamp}")
    
    # For New Relic: convert to UTC
    # Since local_time is naive, we need to assume it's in system timezone
    # For testing, let's assume it's in Europe/Berlin
    berlin_tz = pytz.timezone('Europe/Berlin')
    local_time_berlin = berlin_tz.localize(local_time)
    utc_time = local_time_berlin.astimezone(pytz.UTC)
    new_relic_epoch = str(int(utc_time.timestamp()))
    print(f"New Relic (UTC): {utc_time.isoformat()} -> epoch: {new_relic_epoch}")
    
    # Simulate container environment
    print("\n🐳 CONTAINER ENVIRONMENT SIMULATION")
    print("-" * 40)
    is_container = True
    timezone = pytz.timezone('Europe/Berlin')
    
    # In container, datetime.now() should give Berlin time (due to TZ=Europe/Berlin)
    # But let's simulate it
    container_time = datetime.now()  # This would be Berlin time in container
    print(f"Container time (naive, but actually Berlin): {container_time}")
    
    # Localize to Berlin timezone explicitly
    container_time_berlin = timezone.localize(container_time)
    print(f"Container time (Berlin explicit): {container_time_berlin}")
    
    # For CSV: use Berlin time
    csv_date = container_time_berlin.strftime("%d.%m.%Y")
    csv_time = container_time_berlin.strftime("%H:%M:%S")
    print(f"CSV format: {csv_date} {csv_time}")
    
    # For Splunk: use Berlin time
    splunk_timestamp = container_time_berlin.isoformat()
    print(f"Splunk timestamp: {splunk_timestamp}")
    
    # For New Relic: convert Berlin to UTC
    utc_time = container_time_berlin.astimezone(pytz.UTC)
    new_relic_epoch = str(int(utc_time.timestamp()))
    print(f"New Relic (UTC): {utc_time.isoformat()} -> epoch: {new_relic_epoch}")
    
    # Show time difference
    print("\n⏰ TIME COMPARISON")
    print("-" * 40)
    now_utc = datetime.now(pytz.UTC)
    now_berlin = now_utc.astimezone(pytz.timezone('Europe/Berlin'))
    now_local = datetime.now()
    
    print(f"Current UTC:    {now_utc}")
    print(f"Current Berlin: {now_berlin}")
    print(f"Current Local:  {now_local}")
    print(f"Berlin offset from UTC: {now_berlin.strftime('%z')}")

if __name__ == "__main__":
    test_timezone_conversions()
