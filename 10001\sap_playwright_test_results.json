{"test_info": {"url": "https://oneb-central-dev.launchpad.cfapps.eu10.hana.ondemand.com/site?siteId=1af3caee-8117-49e3-b787-7ad74914b5cb", "timestamp": "2025-07-23T05:26:35.912373+00:00", "browser": "chromium", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "configuration": {"page_timeout": 60000, "navigation_timeout": 30000, "headless": true, "viewport": "1920x1080", "screenshot_enabled": true, "azure_upload_enabled": true}}, "connectivity": {"status_code": 200, "status_text": "", "url": "https://oneb-central-dev.launchpad.cfapps.eu10.hana.ondemand.com/site?siteId=1af3caee-8117-49e3-b787-7ad74914b5cb", "original_url": "https://oneb-central-dev.launchpad.cfapps.eu10.hana.ondemand.com/site?siteId=1af3caee-8117-49e3-b787-7ad74914b5cb", "redirected": false, "headers": {"cache-control": "no-cache, no-store, must-revalidate", "content-security-policy": "script-src 'self' 'unsafe-inline'; frame-ancestors *", "content-type": "text/html", "date": "Wed, 23 Jul 2025 05:26:39 GMT", "strict-transport-security": "max-age=31536000; includeSubDomains; preload;", "x-content-type-options": "nosniff", "x-request-id": "175edf7f-7574-4740-6ea3-5876d9d196a4", "x-vcap-request-id": "175edf7f-7574-4740-6ea3-5876d9d196a4"}, "ok": true}, "page_info": {"title": "<PERSON><PERSON>", "final_url": "https://oneb-central-dev.authentication.eu10.hana.ondemand.com/login", "title_contains_expected": false, "title_matched_keywords": [], "page_loaded": true, "content_length": 2492, "javascript_errors": [], "javascript_error_count": 0, "performance_metrics": {"name": "https://oneb-central-dev.authentication.eu10.hana.ondemand.com/login", "entryType": "navigation", "startTime": 0, "duration": 555.5, "initiatorType": "navigation", "deliveryType": "", "nextHopProtocol": "h2", "renderBlockingStatus": "non-blocking", "workerStart": 0, "redirectStart": 36, "redirectEnd": 332.70000000298023, "fetchStart": 332.70000000298023, "domainLookupStart": 332.70000000298023, "domainLookupEnd": 332.70000000298023, "connectStart": 332.70000000298023, "secureConnectionStart": 332.70000000298023, "connectEnd": 332.70000000298023, "requestStart": 333.90000000596046, "responseStart": 397, "firstInterimResponseStart": 0, "responseEnd": 398, "transferSize": 2831, "encodedBodySize": 2531, "decodedBodySize": 2531, "responseStatus": 200, "serverTiming": [], "unloadEventStart": 0, "unloadEventEnd": 0, "domInteractive": 410.5, "domContentLoadedEventStart": 410.5, "domContentLoadedEventEnd": 410.70000000298023, "domComplete": 555.4000000059605, "loadEventStart": 555.5, "loadEventEnd": 555.5, "type": "navigate", "redirectCount": 1, "activationStart": 0, "criticalCHRestart": 0}}, "performance": {"browser_launch_time": 1.587802, "navigation_time": 1.05906, "total_test_time": 14.693888}, "errors": [], "success": true, "progress": [{"step": "1/6", "message": "Initializing Playwright environment", "success": true, "timestamp": "2025-07-23T05:26:35.920349+00:00"}, {"step": "1/6", "message": "Playwright initialized successfully", "success": true, "timestamp": "2025-07-23T05:26:36.764494+00:00"}, {"step": "2/6", "message": "Launching chromium browser...", "success": true, "timestamp": "2025-07-23T05:26:36.764494+00:00"}, {"step": "2/6", "message": "Browser launched in 1.59s", "success": true, "timestamp": "2025-07-23T05:26:38.352296+00:00"}, {"step": "3/6", "message": "Creating and configuring page...", "success": true, "timestamp": "2025-07-23T05:26:38.352296+00:00"}, {"step": "3/6", "message": "<PERSON> created and configured successfully", "success": true, "timestamp": "2025-07-23T05:26:38.598493+00:00"}, {"step": "4/6", "message": "Navigating to target URL...", "success": true, "timestamp": "2025-07-23T05:26:38.598493+00:00"}, {"step": "4/6", "message": "Navigation completed in 1.06s (Status: 200)", "success": true, "timestamp": "2025-07-23T05:26:39.657553+00:00"}, {"step": "5/6", "message": "Analyzing page content and performance...", "success": true, "timestamp": "2025-07-23T05:26:44.017937+00:00"}, {"step": "5/6", "message": "Page analysis completed successfully", "success": true, "timestamp": "2025-07-23T05:26:47.068721+00:00"}, {"step": "6/6", "message": "Taking screenshot of the page...", "success": true, "timestamp": "2025-07-23T05:26:47.068721+00:00"}, {"step": "6/6", "message": "Screenshot saved locally to ./sap_screenshot.png", "success": true, "timestamp": "2025-07-23T05:26:50.614237+00:00"}, {"step": "COMPLETE", "message": "Test completed successfully in 14.69s", "success": true, "timestamp": "2025-07-23T05:26:50.706758+00:00"}], "screenshots": [{"filename": "10001_20250723-072643_AFTER_NAVIGATION.png", "local_path": "/tmp/monitoring/img\\10001_20250723-072643_AFTER_NAVIGATION.png", "step_name": "AFTER_NAVIGATION", "timestamp": "20250723-072643", "url": "https://oneb-central-dev.authentication.eu10.hana.ondemand.com/login", "title": "<PERSON><PERSON>", "azure_uploaded": false}, {"filename": "10001_20250723-072650_FINAL_PAGE.png", "local_path": "/tmp/monitoring/img\\10001_20250723-072650_FINAL_PAGE.png", "step_name": "FINAL_PAGE", "timestamp": "20250723-072650", "url": "https://oneb-central-dev.authentication.eu10.hana.ondemand.com/login", "title": "<PERSON><PERSON>", "azure_uploaded": false}], "screenshot_taken": true, "screenshot_path": "/tmp/monitoring/img\\10001_20250723-072650_FINAL_PAGE.png"}