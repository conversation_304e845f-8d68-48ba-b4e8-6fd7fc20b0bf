#!/bin/bash

#############################################################
#                USER CONFIGURATION SECTION                 #
#############################################################
# Modify these values according to your Azure environment

# Script Identifier (used for naming resources and secrets)
SCRIPT="10001"

# Azure Resource Group and Location
RESOURCE_GROUP="RG-EDGMON-RUN"
LOCATION="Germany West Central"

# Azure Services Names
KEYVAULT_NAME="scriptsmonitoring2025"
ACR_NAME="scriptmonregistry2025"
STORAGE_ACCOUNT="scriptsmonitoring2025"
MANAGED_IDENTITY="scriptmonregistry2025-identity"
CONTAINER_APP_ENV="managedEnvironment-RGEDGMONRUN-b130"

# Application Configuration
CONTAINER_APP_NAME="sap-monitoring-${SCRIPT}-app"
IMAGE_NAME="sap-monitoring-${SCRIPT}"
IMAGE_TAG="latest"

# SAP Target URL (will be stored in Key Vault)
SAP_TARGET_URL="https://oneb-central-dev.launchpad.cfapps.eu10.hana.ondemand.com/site?siteId=1af3caee-8117-49e3-b787-7ad74914b5cb"

# Container Resources
CPU="1.0"
MEMORY="2.0Gi"
MIN_REPLICAS="0"
MAX_REPLICAS="1"

#############################################################
#                 DEPLOYMENT SCRIPT LOGIC                   #
#############################################################
# Do not modify below this line unless you know what you're doing

echo "🚀 Deploying SAP Monitoring application ${SCRIPT} on Azure Container Apps"

# 1. Connect to Azure (if necessary)
echo "🔐 Checking Azure connection..."
az account show > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ Not logged in to Azure. Please run 'az login' first."
    exit 1
fi

echo "✅ Azure connection verified"

# 2. Build and push Docker image to ACR
echo "🔨 Building and pushing Docker image..."
az acr login --name $ACR_NAME

# Build the image
docker build -t $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG .

# Push to ACR
docker push $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG

echo "✅ Docker image pushed to ACR"

# 3. Add secrets to Key Vault (if not already done)
echo "🔐 Configuring secrets in Key Vault..."

# Secrets for SAP monitoring
az keyvault secret set --vault-name $KEYVAULT_NAME --name "${SCRIPT}-target-url" --value "$SAP_TARGET_URL" --output none

echo "✅ Secrets configured in Key Vault"

# 4. Create or update Container App
echo "📦 Creating/updating Container App..."

# Check if container app exists
EXISTING_APP=$(az containerapp show --name $CONTAINER_APP_NAME --resource-group $RESOURCE_GROUP --query name --output tsv 2>/dev/null)

if [ -z "$EXISTING_APP" ]; then
    echo "🆕 Creating new Container App..."
    az containerapp create \
        --name $CONTAINER_APP_NAME \
        --resource-group $RESOURCE_GROUP \
        --environment $CONTAINER_APP_ENV \
        --image $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG \
        --registry-server $ACR_NAME.azurecr.io \
        --registry-identity $MANAGED_IDENTITY \
        --cpu $CPU \
        --memory $MEMORY \
        --min-replicas $MIN_REPLICAS \
        --max-replicas $MAX_REPLICAS \
        --env-vars \
            TARGET_URL=secretref:target-url \
            AZURE_KEYVAULT_NAME=$KEYVAULT_NAME \
            AZURE_STORAGE_ACCOUNT=$STORAGE_ACCOUNT \
            AZURE_CONTAINER_NAME=$SCRIPT \
            TAKE_SCREENSHOT=true \
            UPLOAD_TO_AZURE=true \
        --secrets \
            target-url=keyvaultref:$KEYVAULT_NAME/${SCRIPT}-target-url,identityref:$MANAGED_IDENTITY \
        --system-assigned
else
    echo "🔄 Updating existing Container App..."
    az containerapp update \
        --name $CONTAINER_APP_NAME \
        --resource-group $RESOURCE_GROUP \
        --image $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG
fi

echo "✅ Container App deployment completed"

# 5. Display deployment information
echo ""
echo "📋 DEPLOYMENT SUMMARY"
echo "====================="
echo "🏷️  Script ID: $SCRIPT"
echo "📦 Container App: $CONTAINER_APP_NAME"
echo "🖼️  Image: $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG"
echo "🔐 Key Vault: $KEYVAULT_NAME"
echo "💾 Storage Account: $STORAGE_ACCOUNT"
echo ""
echo "🎯 Target URL: $SAP_TARGET_URL"
echo "📸 Screenshots: Enabled with Azure upload"
echo ""
echo "✅ Deployment completed successfully!"
echo ""
echo "📊 To monitor the application:"
echo "   az containerapp logs show --name $CONTAINER_APP_NAME --resource-group $RESOURCE_GROUP --follow"
echo ""
echo "🔧 To manually trigger execution:"
echo "   az containerapp job start --name $CONTAINER_APP_NAME --resource-group $RESOURCE_GROUP"
