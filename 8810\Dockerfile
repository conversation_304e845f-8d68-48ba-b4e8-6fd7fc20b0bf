FROM mcr.microsoft.com/playwright/python:latest

# Set working directory
WORKDIR /app

# Install Azure dependencies
RUN pip install azure-identity azure-keyvault-secrets azure-storage-blob

# Copy script files
COPY ng_mon_playw_puw_availability.py /app/
COPY requirements.txt /app/

# Install additional project dependencies
RUN pip install -r requirements.txt

# Configure environment variables for Key Vault
ENV KEY_VAULT_NAME="b2smepcw-kv-12345"
ENV KEY_VAULT_SECRET_NAME="PUW-CREDENTIALS"

# Execute the script
CMD ["python", "ng_mon_playw_puw_availability.py"]
