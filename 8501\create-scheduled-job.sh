#!/bin/bash

# Configuration
RESOURCE_GROUP="RG-EDGMON-RUN"
KEYVAULT_NAME="scriptsmonitoring2025"
ACR_NAME="scriptmonregistry2025"
STORAGE_ACCOUNT="scriptsmonitoring2025"
MANAGED_IDENTITY="scriptmonregistry2025-identity"
CONTAINER_APP_ENV="managedEnvironment-RGEDGMONRUN-b130"
SCRIPT="8501"
JOB_NAME="powercloud-monitoring-${SCRIPT}-job"
IMAGE_NAME="powercloud-monitoring-${SCRIPT}"
IMAGE_TAG="latest"

echo "🚀 Creating Container App Job for Powercloud Monitoring"
echo "====================================================="

# Verify Azure CLI login
echo "📋 Checking Azure CLI login..."
az account show > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ Please login to Azure with 'az login'"
    exit 1
fi

# Get the Managed Identity Client ID
echo "🔑 Getting Managed Identity Client ID..."
IDENTITY_ID=$(az identity show --name $MANAGED_IDENTITY --resource-group $RESOURCE_GROUP --query id -o tsv)
CLIENT_ID=$(az identity show --name $MANAGED_IDENTITY --resource-group $RESOURCE_GROUP --query clientId -o tsv)

if [ -z "$IDENTITY_ID" ] || [ -z "$CLIENT_ID" ]; then
    echo "❌ Failed to get Managed Identity details"
    exit 1
fi

echo "✅ Using Managed Identity: $MANAGED_IDENTITY (Client ID: $CLIENT_ID)"

# Create the Container App Job
echo ""
echo "🔧 Creating Container App Job..."
az containerapp job create \
    --name $JOB_NAME \
    --resource-group $RESOURCE_GROUP \
    --environment $CONTAINER_APP_ENV \
    --trigger-type "Schedule" \
    --cron-expression "*/5 * * * *" \
    --image $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG \
    --user-assigned $IDENTITY_ID \
    --registry-server $ACR_NAME.azurecr.io \
    --registry-identity $IDENTITY_ID \
    --env-vars AZURE_CLIENT_ID=$CLIENT_ID SCRIPT_ID=$SCRIPT \
    --cpu 0.5 \
    --memory 1.0Gi \
    --parallelism 1 \
    --replica-completion-count 1 \
    --replica-timeout 600 \
    --replica-retry-limit 2

if [ $? -ne 0 ]; then
    echo "❌ Failed to create job"
    exit 1
fi

echo "✅ Container App Job created successfully"
echo ""
echo "🎉 Job Configuration:"
echo "- Name: $JOB_NAME"
echo "- Schedule: Every 5 minutes (*/5 * * * *)"
echo "- Timeout: 10 minutes per execution"
echo "- Retry: 2 attempts on failure"
echo ""
echo "To modify the schedule, use:"
echo "az containerapp job update --name $JOB_NAME --resource-group $RESOURCE_GROUP --cron-expression \"0 */30 * * * *\""

